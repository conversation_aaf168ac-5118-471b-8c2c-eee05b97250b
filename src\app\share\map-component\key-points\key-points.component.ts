import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Subject, BehaviorSubject, Subscription } from 'rxjs';
import { KeyPointService } from '../service';
import { takeUntil } from 'rxjs/operators';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { ModalManagerService } from 'src/app/execut/services/modal-manager.service';
import { ClockInModalComponent } from 'src/app/execut/modal/clock-in-modal/clock-in-modal.component';
import { DetailsMode } from 'src/app/@core/base/environment';

interface KeyPoint {
  pointName: string;
  isItRaining: string;
  state: string;
  picCode?: string | string[];
  reason?: string;
}

@Component({
  selector: 'app-key-points',
  templateUrl: './key-points.component.html',
  styleUrls: ['./key-points.component.scss']
})
export class KeyPointsComponent implements OnInit, OnDestroy {
  // 禁用点击事件
  @Input() disabledClick = true;
  // 任务编码
  @Input() taskCode: string;
  // 展示关闭按钮
  @Input() showClose = true;
  // 是否显示数据状态列（打卡数据/未巡检原因）
  @Input() showDataStatus = true;
  // 关键点数据
  @Input() keyPoints: KeyPoint[] = [];
  @Input() loading$: BehaviorSubject<boolean>;
  @Input() keyPoints$: BehaviorSubject<KeyPoint[]>;
  // 当前实时坐标（从ExecutComponent传入）
  @Input() currentCoordinate: number[];

  loading = true;
  // 原始关键点数据备份，用于搜索过滤时不改变源数据
  private originalKeyPoints: KeyPoint[] = [];
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  private subscriptions: Subscription[] = [];

  constructor(
    private modalCtrl: ModalController, private keyPointService: KeyPointService,
    private toastSer: ToastService, private modalManagerService: ModalManagerService,
  ) { }

  ngOnInit() {
    if (this.showClose === false) {
      // 独立模式，组件内自行请求
      this.getkeyPoints();
    } else {
      // 订阅父组件传递的BehaviorSubject
      if (this.loading$) {
        this.subscriptions.push(this.loading$.subscribe(val => this.loading = val));
      }
      if (this.keyPoints$) {
        this.subscriptions.push(this.keyPoints$.subscribe(val => {
          this.keyPoints = val;
          // 保存原始数据备份，用于搜索过滤
          this.originalKeyPoints = [...val];
        }));
      }
    }
  }

  /**
   * 获取关键点
   */
  getkeyPoints() {
    this.loading = true;
    this.keyPointService.getKeyPointsByTaskCode(this.taskCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe(result => {
        const { code, data, msg } = result;
        if (code === 0) {
          this.keyPoints = data || [];
          // 保存原始数据备份，用于搜索过滤
          this.originalKeyPoints = [...(data || [])];
        } else {
          this.toastSer.presentToast(msg, 'danger');
        }
        this.loading = false;
      }, () => {
        this.loading = false;
      });
  }

  /**
   * 搜索框输入事件处理函数
   * @param event 输入事件
   */
  filterItems(event: any) {
    const searchText = event.detail?.value?.toLowerCase() || '';

    // 确保有原始数据备份
    if (this.originalKeyPoints.length === 0 && this.keyPoints.length > 0) {
      this.originalKeyPoints = [...this.keyPoints];
    }

    if (!searchText) {
      // 如果搜索文本为空，恢复原始数据
      this.keyPoints = [...this.originalKeyPoints];
      return;
    }

    // 基于原始数据进行模糊匹配，不改变源数据
    const filteredPoints = this.originalKeyPoints.filter(point =>
      point.pointName.toLowerCase().includes(searchText)
    );

    // 更新显示的关键点列表
    this.keyPoints = filteredPoints;
  }

  /**
   * 点击关键点
   * @param keyPoint 关键点
   */
  async onClick(keyPoint: any): Promise<void> {
    // 只有状态为 "未巡检" 时，才能点击
    // if (keyPoint.state !== '未巡') {
    //   return;
    // }
    await this.modalManagerService.createModalWithGuard('punchIn', {
      component: ClockInModalComponent,
      cssClass: 'camera-list-modal',
      componentProps: {
        taskCode: this.taskCode,
        currentKeyPoint: keyPoint,
        currentCoordinate: this.currentCoordinate, // 传递实时坐标
      },
    }, (data) => {
      if (data && data.keyPointId) {
        const updatedId = data.keyPointId;

        // 如果网络通畅且提交成功，重新获取关键点数据
        if (data.shouldRefreshKeyPoints) {
          this.getkeyPoints();
        } else {
          // 否则只更新本地状态
          const idx = this.keyPoints.findIndex(p => p['id'] === updatedId);
          if (idx !== -1) {
            this.keyPoints[idx].state = '已巡';
            // 同步更新keyPoints$
            if (this.keyPoints$) {
              this.keyPoints$.next([...this.keyPoints]);
            }
          }
        }
      }
    });
  }

  /**
   * 点击数据状态（打开ClockInModalComponent并跳转到对应tab）
   * @param keyPoint 关键点
   * @param event 点击事件
   * @param tabType tab类型：'clockIn' 或 'reason'
   */
  async onDataStatusClick(keyPoint: any, event: Event, tabType: 'clockIn' | 'reason'): Promise<void> {
    event.stopPropagation(); // 阻止事件冒泡

    // 打开ClockInModalComponent并设置对应的tab和查看模式
    await this.modalManagerService.createModalWithGuard('punchIn', {
      component: ClockInModalComponent,
      cssClass: 'camera-list-modal',
      componentProps: {
        taskCode: this.taskCode,
        currentKeyPoint: keyPoint,
        initialTab: tabType, // 传递初始tab类型
        modelMode: DetailsMode.SEE, // 设置为查看模式
        isDetailMode: true, // 设置为详情模式
      },
    }, (data) => {
      if (data && data.keyPointId) {
        const updatedId = data.keyPointId;

        // 如果网络通畅且提交成功，重新获取关键点数据
        if (data.shouldRefreshKeyPoints) {
          this.getkeyPoints();
        } else {
          // 否则只更新本地状态
          const idx = this.keyPoints.findIndex(p => p['id'] === updatedId);
          if (idx !== -1) {
            this.keyPoints[idx].state = '已巡';
            // 同步更新keyPoints$
            if (this.keyPoints$) {
              this.keyPoints$.next([...this.keyPoints]);
            }
          }
        }
      }
    });
  }

  /**
   * 检查是否有打卡数据
   * @param keyPoint 关键点
   */
  hasPicCode(keyPoint: any): boolean {
    return keyPoint.picCode &&
           ((Array.isArray(keyPoint.picCode) && keyPoint.picCode.length > 0) ||
            (typeof keyPoint.picCode === 'string' && keyPoint.picCode.trim() !== ''));
  }

  /**
   * 检查是否有未巡检原因
   * @param keyPoint 关键点
   */
  hasReason(keyPoint: any): boolean {
    return keyPoint.reason && keyPoint.reason.trim() !== '';
  }



  /**
   * 关闭当前弹窗
   */
  close() {
    this.modalCtrl.dismiss();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.destroy$.next();
    this.destroy$.complete();
  }
}
