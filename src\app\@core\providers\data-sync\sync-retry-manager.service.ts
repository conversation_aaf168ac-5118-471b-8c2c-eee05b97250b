import { Injectable } from '@angular/core';
import { SyncCacheManagerService } from './sync-cache-manager.service';
import { 
  SyncDataType, 
  SyncCacheItem, 
  FailedDataGroup 
} from './types/sync-data.types';

/**
 * 数据同步重试管理服务
 * 负责重试机制和失败数据处理，包括重试计数、失败数据导出等功能
 */
@Injectable({
  providedIn: 'root'
})
export class SyncRetryManagerService {

  /**
   * 最大重试次数（可根据业务需求调整）
   */
  private readonly MAX_RETRY = 5;

  constructor(private cacheManager: SyncCacheManagerService) { }

  /**
   * 获取最大重试次数
   */
  getMaxRetry(): number {
    return this.MAX_RETRY;
  }

  /**
   * 检查数据项是否已达到最大重试次数
   */
  isMaxRetryReached(item: SyncCacheItem): boolean {
    return item.retryCount >= this.MAX_RETRY;
  }

  /**
   * 过滤出有效的（未达到最大重试次数的）数据项
   */
  filterValidItems(items: SyncCacheItem[]): SyncCacheItem[] {
    return items.filter(item => !this.isMaxRetryReached(item));
  }

  /**
   * 过滤出失败的（已达到最大重试次数的）数据项
   */
  filterFailedItems(items: SyncCacheItem[]): SyncCacheItem[] {
    return items.filter(item => this.isMaxRetryReached(item));
  }

  /**
   * 增加数据项的重试次数
   */
  incrementRetryCount(item: SyncCacheItem): SyncCacheItem {
    return {
      ...item,
      retryCount: item.retryCount + 1
    };
  }

  /**
   * 批量增加数据项的重试次数
   */
  incrementBatchRetryCount(items: SyncCacheItem[]): SyncCacheItem[] {
    return items.map(item => this.incrementRetryCount(item));
  }

  /**
   * 重置数据项的重试次数
   */
  resetRetryCount(item: SyncCacheItem): SyncCacheItem {
    return {
      ...item,
      retryCount: 0
    };
  }

  /**
   * 批量重置数据项的重试次数
   */
  resetBatchRetryCount(items: SyncCacheItem[]): SyncCacheItem[] {
    return items.map(item => this.resetRetryCount(item));
  }

  /**
   * 获取所有达到最大重试次数的失败数据
   */
  async getFailedData(): Promise<FailedDataGroup[]> {
    const types = Object.values(SyncDataType);
    const failedData: FailedDataGroup[] = [];

    for (const type of types) {
      const cacheList = await this.cacheManager.getCache(type);
      const failedItems = this.filterFailedItems(cacheList);
      if (failedItems.length > 0) {
        failedData.push({ type, items: failedItems });
      }
    }

    return failedData;
  }

  /**
   * 获取失败数据的总数量
   */
  async getFailedDataCount(): Promise<number> {
    const failedData = await this.getFailedData();
    return failedData.reduce((total, group) => total + group.items.length, 0);
  }

  /**
   * 手动重试失败的数据（重置重试次数）
   */
  async manualRetryFailedData(): Promise<void> {
    const failedData = await this.getFailedData();

    for (const { type, items } of failedData) {
      // 重置重试次数
      const resetItems = this.resetBatchRetryCount(items);
      await this.cacheManager.updateCacheItems(type, resetItems);
    }

    console.log(`[SyncRetryManager] 已重置 ${failedData.length} 个类型的失败数据重试次数`);
  }

  /**
   * 清理所有失败数据（永久删除）
   */
  async clearFailedData(): Promise<number> {
    const failedData = await this.getFailedData();
    let totalCleared = 0;

    for (const { type, items } of failedData) {
      const idsToRemove = items.map(item => item.id);
      await this.cacheManager.removeBatchFromCache(type, idsToRemove);
      totalCleared += items.length;
    }

    console.log(`[SyncRetryManager] 已清理 ${totalCleared} 个失败数据项`);
    return totalCleared;
  }

  /**
   * 导出失败数据到文本格式
   */
  async exportFailedDataToText(): Promise<string> {
    const failedData = await this.getFailedData();

    if (failedData.length === 0) {
      return '没有失败的数据需要导出';
    }

    const exportData = {
      exportTime: new Date().toISOString(),
      deviceInfo: {
        userAgent: navigator.userAgent,
        platform: navigator.platform
      },
      failedData: failedData.map(({ type, items }) => ({
        type,
        count: items.length,
        items: items.map(item => ({
          id: item.id,
          data: item.data,
          timestamp: new Date(item.timestamp).toISOString(),
          retryCount: item.retryCount,
          uploadUrl: item.uploadUrl
        }))
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 获取重试统计信息
   */
  async getRetryStats(): Promise<{
    totalItems: number;
    validItems: number;
    failedItems: number;
    retryDistribution: { [retryCount: number]: number };
  }> {
    const types = Object.values(SyncDataType);
    let totalItems = 0;
    let validItems = 0;
    let failedItems = 0;
    const retryDistribution: { [retryCount: number]: number } = {};

    for (const type of types) {
      const cacheList = await this.cacheManager.getCache(type);
      totalItems += cacheList.length;

      for (const item of cacheList) {
        if (this.isMaxRetryReached(item)) {
          failedItems++;
        } else {
          validItems++;
        }

        retryDistribution[item.retryCount] = (retryDistribution[item.retryCount] || 0) + 1;
      }
    }

    return {
      totalItems,
      validItems,
      failedItems,
      retryDistribution
    };
  }

  /**
   * 检查是否有需要重试的数据
   */
  async hasRetryableData(): Promise<boolean> {
    const types = Object.values(SyncDataType);

    for (const type of types) {
      const cacheList = await this.cacheManager.getCache(type);
      const validItems = this.filterValidItems(cacheList);
      if (validItems.length > 0) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取按类型分组的重试统计
   */
  async getRetryStatsByType(): Promise<{
    type: SyncDataType;
    total: number;
    valid: number;
    failed: number;
    avgRetryCount: number;
  }[]> {
    const types = Object.values(SyncDataType);
    const stats = [];

    for (const type of types) {
      const cacheList = await this.cacheManager.getCache(type);
      const validItems = this.filterValidItems(cacheList);
      const failedItems = this.filterFailedItems(cacheList);

      const totalRetryCount = cacheList.reduce((sum, item) => sum + item.retryCount, 0);
      const avgRetryCount = cacheList.length > 0 ? totalRetryCount / cacheList.length : 0;

      stats.push({
        type,
        total: cacheList.length,
        valid: validItems.length,
        failed: failedItems.length,
        avgRetryCount: Math.round(avgRetryCount * 100) / 100
      });
    }

    return stats;
  }
}
