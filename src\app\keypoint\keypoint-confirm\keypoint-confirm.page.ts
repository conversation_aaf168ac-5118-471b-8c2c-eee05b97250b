import { ChangeDetector<PERSON><PERSON>, Component, HostL<PERSON>ener, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Nav<PERSON><PERSON>roller, AlertController, LoadingController, ToastController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { KeypointConfirmService, UnconfirmedKeyPoint, ConfirmParams } from './keypoint-confirm.service';
import { MapComponent } from '../../share/map-component/map/map.component';
import { MapService } from '../../share/map-component/service/map.service';

@Component({
  selector: 'app-keypoint-confirm',
  templateUrl: './keypoint-confirm.page.html',
  styleUrls: ['./keypoint-confirm.page.scss'],
})
export class KeypointConfirmPage implements OnInit, OnDestroy {
  @ViewChild('mapComponent') mapComponent: MapComponent;

  unconfirmedPoints: UnconfirmedKeyPoint[] = [];
  unconfirmedCount = 0;
  isLoading = false;

  private destroy$ = new Subject<void>();

  constructor(
    private nav: NavController,
    private cdr: ChangeDetectorRef,
    private keypointConfirmService: KeypointConfirmService,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private toastController: ToastController,
    private mapService: MapService
  ) {}

  ngOnInit() {
    this.loadUnconfirmedPoints();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  goBack(): void {
    this.nav.back();
  }

  /**
   * 加载未确认关键点数据
   */
  async loadUnconfirmedPoints() {
    this.isLoading = true;
    
    try {
      // 获取未确认关键点列表
      this.keypointConfirmService.getUnconfirmedList({})
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (result) => {
            if (result.code === 0) {
              this.unconfirmedPoints = result.data.map(point => ({
                ...point,
                selected: false
              }));
              this.cdr.detectChanges();
            }
          },
          error: (error) => {
            console.error('获取未确认关键点列表失败:', error);
            this.showToast('获取数据失败，请重试');
          }
        });

      // 获取未确认关键点个数
      this.keypointConfirmService.getUnconfirmedCount({})
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (result) => {
            if (result.code === 0) {
              this.unconfirmedCount = parseInt(result.data) || 0;
              this.cdr.detectChanges();
            }
          },
          error: (error) => {
            console.error('获取未确认关键点个数失败:', error);
          }
        });
    } finally {
      this.isLoading = false;
    }
  }

  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event: any): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.goBack();
    });
  }
  /**
   * 列表项定位事件 - 点击列表项时定位到地图上对应的关键点
   */
  onPointLocate(point: UnconfirmedKeyPoint) {
    try {
      let longitude = point.longitude;
      let latitude = point.latitude;
      
      // 如果没有直接的坐标信息，从geom字段解析
      if (!longitude || !latitude) {
        try {
          const geom = JSON.parse(point.geom);
          if (geom.type === 'Point' && geom.coordinates && geom.coordinates.length >= 2) {
            longitude = geom.coordinates[0];
            latitude = geom.coordinates[1];
          }
        } catch (error) {
          console.warn('解析关键点坐标失败:', point.pointCode, error);
          this.showToast('无法获取关键点坐标信息');
          return;
        }
      }
      
      // 验证坐标有效性
      if (!longitude || !latitude || longitude === 0 || latitude === 0) {
        this.showToast('关键点坐标信息无效');
        return;
      }
      
      const coordinate: [number, number] = [longitude, latitude];
      
      // 使用MapService移动地图到指定坐标，设置合适的缩放级别
      this.mapService.moveMapToCoordinate(coordinate, 800, 16);
      
      // 显示定位成功提示
      this.showToast(`已定位到关键点: ${point.pointName}`);
      
    } catch (error) {
      console.error('定位关键点失败:', error);
      this.showToast('定位失败，请重试');
    }
  }

  /**
   * 单个关键点确认操作
   */
  async confirmSingle(point: UnconfirmedKeyPoint, isOk: boolean) {
    // 显示确认对话框
    const alert = await this.alertController.create({
      header: isOk ? '确认采用' : '确认删除',
      message: `确定要${isOk ? '采用' : '删除'}关键点 "${point.pointName}" 吗？`,
      buttons: [
        {
          text: '取消',
          role: 'cancel'
        },
        {
          text: '确定',
          handler: () => {
            this.processSingleOperation(point, isOk);
          }
        }
      ]
    });

    await alert.present();
  }

  /**
   * 处理单个关键点操作
   */
  private async processSingleOperation(point: UnconfirmedKeyPoint, isOk: boolean) {
    const loading = await this.loadingController.create({
      message: isOk ? '正在采用...' : '正在删除...'
    });
    await loading.present();

    try {
      const result = await this.keypointConfirmService.confirmKeyPoint({
        isOk: isOk ? 'yes' : 'no',
        pointCode: point.pointCode
      }).toPromise();

      if (result.code === 0) {
        this.showToast(isOk ? '采用成功' : '删除成功');
        // 从列表中移除已处理的关键点
        this.removePointFromList(point.pointCode);
        // 重新加载数据以确保数据同步
        this.loadUnconfirmedPoints();
      } else {
        this.showToast(result.msg || '操作失败');
      }
    } catch (error) {
      console.error('确认操作失败:', error);
      this.showToast('操作失败，请重试');
    } finally {
      await loading.dismiss();
    }
  }

  /**
   * 从列表中移除指定的关键点
   */
  private removePointFromList(pointCode: string) {
    // 从未确认列表中移除
    this.unconfirmedPoints = this.unconfirmedPoints.filter(p => p.pointCode !== pointCode);
    // 更新计数
    this.unconfirmedCount = this.unconfirmedPoints.length;
    // 触发变更检测
    this.cdr.detectChanges();
  }

  /**
   * 显示提示信息
   */
  private async showToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 2000,
      position: 'bottom'
    });
    await toast.present();
  }

  /**
   * 刷新数据
   */
  async refresh(event?: any) {
    await this.loadUnconfirmedPoints();
    if (event) {
      event.target.complete();
    }
  }

  /**
   * 跟踪函数，用于优化列表渲染性能
   */
  trackByPointCode(index: number, point: UnconfirmedKeyPoint): string {
    return point.pointCode;
  }
}