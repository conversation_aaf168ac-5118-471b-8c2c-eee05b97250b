import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { UnconfirmedKeyPoint } from '../../keypoint-confirm.service';

@Component({
  selector: 'app-confirm-list-view',
  templateUrl: './confirm-list-view.component.html',
  styleUrls: ['./confirm-list-view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfirmListViewComponent implements OnInit {
  @Input() points: UnconfirmedKeyPoint[] = [];
  
  @Output() confirmSingle = new EventEmitter<{point: UnconfirmedKeyPoint, isOk: boolean}>();
  @Output() pointLocate = new EventEmitter<UnconfirmedKeyPoint>();

  constructor() { }

  ngOnInit() {}

  /**
   * 处理列表项点击事件（用于地图定位）
   */
  onItemClick(point: UnconfirmedKeyPoint, event: Event) {
    // 检查点击的是否为操作按钮，如果是则不触发定位
    const target = event.target as HTMLElement;
    if (target.closest('.action-buttons')) {
      return;
    }
    
    // 触发地图定位事件
    this.pointLocate.emit(point);
  }

  /**
   * 处理单个确认操作
   */
  onConfirmSingle(point: UnconfirmedKeyPoint, isOk: boolean, event: Event) {
    event.stopPropagation();
    this.confirmSingle.emit({ point, isOk });
  }

  /**
   * 跟踪函数，用于优化列表渲染性能
   */
  trackByPointCode(index: number, point: UnconfirmedKeyPoint): string {
    return point.pointCode;
  }
}