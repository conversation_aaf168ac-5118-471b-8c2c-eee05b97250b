import { AfterViewInit, Component, HostListener, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Coordinate } from 'ol/coordinate';
import { MapComponent } from '../map/map.component';
import { KeyPointInfo } from 'src/app/execut/class/key-point';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DetailsMode } from 'src/app/@core/base/environment';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { ExecutService } from 'src/app/execut/execut.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToastService } from 'src/app/@core/providers/toast.service';

@Component({
  selector: 'app-location-select',
  templateUrl: './location-select.component.html',
  styleUrls: ['./location-select.component.scss'],
})
export class LocationSelectComponent implements AfterViewInit, OnInit, OnDestroy {
  @ViewChild('ostMap', { static: false }) ostMap!: MapComponent;
  @Input() coordinate: Coordinate = [116.4047470, 39.9069387];
  @Input() taskCode!: string;
  @Input() modelInfo: KeyPointInfo = new KeyPointInfo();
  @Input() modelMode!: DetailsMode;

  layerId: string[] = ['inspect_point'];
  DetailsMode = DetailsMode;

  isItRaining = false;
  showMap = true;
  infoFormGroup!: FormGroup;

  // 步骤控制
  currentStep: 'location' | 'info' = 'location';
  showMask: boolean = true;
  isCollecting: boolean = false;
  countdown: number = 5;
  locationConfirmed: boolean = false;

  private collectedLocations: Array<{coordinate: Coordinate, accuracy: number}> = [];
  private bestLocation: {coordinate: Coordinate, accuracy: number} | null = null;

  // 偏差值状态管理
  accuracyStatus: 'good' | 'poor' | 'unknown' = 'unknown';
  showAccuracyTip: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private modalController: ModalController, private fb: FormBuilder,
    private userSer: UserInfoService, private netSer: ExecutService,
    private toastSer: ToastService,
  ) { }

  ngOnInit(): void {
    this.initializeData();
    this.createForm();
  }

  ngAfterViewInit(): void {
    this.initCenterPoint();
  }

  /**
   * 初始化数据
   */
  private initializeData(): void {
    if (this.modelMode === DetailsMode.ADD) {
      this.modelInfo.depCode = this.userSer.depCode;
      // 新增模式下，从位置选择步骤开始
      this.currentStep = 'location';
      this.showMask = true;
      this.locationConfirmed = false;
    } else {
      // 编辑模式下，直接跳到信息填写步骤
      this.coordinate = this.modelInfo.pointGeom;
      this.isItRaining = this.convertStringToBoolean(this.modelInfo.isItRaining);
      this.currentStep = 'info';
      this.showMask = false;
      this.locationConfirmed = true;
    }
  }

  /**
   * 创建表单
   */
  private createForm(): void {
    this.infoFormGroup = this.fb.group({
      depCode: [this.modelInfo.depCode, [Validators.required]],
      depName: [this.modelInfo.depName],
      pointName: [this.modelInfo.pointName,[Validators.required]],
      inspectionMethod: [this.modelInfo.inspectionMethod,[Validators.required]],
      bufferRange: [this.modelInfo.bufferRange],
      isItRaining: [this.modelInfo.isItRaining],
      taskCode: [this.modelInfo.taskCode || this.taskCode],
    });
  }

  /**
   * 将字符串转换为布尔值
   */
  private convertStringToBoolean(value: string): boolean {
    return value === '是';
  }

  /**
   * 将布尔值转换为字符串
   */
  private convertBooleanToString(value: boolean): string {
    return value ? '是' : '否';
  }

  /**
   * 地图初始化中心点
   */
  private initCenterPoint(): void {
    if (this.isValidCoordinate(this.coordinate)) {
      this.ostMap.setCurrentLocation(this.coordinate);
    } else {
      // 如果没有有效坐标，不进行定位，等待用户手动采集
      console.log('没有有效坐标，等待用户手动采集');
    }
  }

  /**
   * 检查坐标是否有效
   * @param coordinate 坐标
   * @returns 是否有效
   */
  private isValidCoordinate(coordinate: any): boolean {
    return coordinate &&
      !(Array.isArray(coordinate) && coordinate[0] === 0 && coordinate[1] === 0);
  }

  /**
   * 切换地图/表单视图
   */
  segmentChanged(ev: any): void {
    const segment = ev.detail.value;
    if (segment === 'map') {
      this.currentStep = 'location';
      this.showMap = true;
    } else if (segment === 'basic') {
      // 只有在位置已确认的情况下才能切换到基础信息
      if (this.locationConfirmed) {
        this.currentStep = 'info';
        this.showMap = false;
      } else {
        // 如果位置未确认，提示用户先确认位置
        this.toastSer.presentToast('请先确认位置信息', 'warning');
        // 重置segment到map，使用更可靠的方式
        this.resetSegmentToMap();
      }
    }
  }

  /**
   * 重置segment到地图选项
   */
  private resetSegmentToMap(): void {
    setTimeout(() => {
      const segment = document.querySelector('ion-segment') as any;
      if (segment) {
        segment.value = 'map';
      }
    }, 100);
  }

  /**
   * 部门值改变
   */
  valueChange(ev: { name: string }): void {
    this.infoFormGroup.get('depName')?.setValue(ev.name);
  }

  /**
   * 确定提交
   */
  onConfirm(): void {
    if (this.currentStep === 'location') {
      // 位置选择步骤，确认位置
      this.confirmLocation();
    } else if (this.currentStep === 'info') {
      // 基础信息步骤，提交表单
      if (!this.infoFormGroup.valid) {
        this.toastSer.presentToast('请填写必填项', 'warning');
        return;
      }
      const formData = this.prepareFormData();
      this.onSubmit(formData);
    }
  }

  /**
   * 确认位置
   */
  confirmLocation(): void {
    if (!this.isValidCoordinate(this.coordinate)) {
      this.toastSer.presentToast('请先采集位置信息', 'warning');
      return;
    }

    this.locationConfirmed = true;
    this.currentStep = 'info';
    this.showMap = false;

    // 自动切换到基础信息tab
    this.switchToBasicSegment();

    this.toastSer.presentToast('位置确认成功，请填写基础信息', 'success');
  }

  /**
   * 切换到基础信息segment
   */
  private switchToBasicSegment(): void {
    setTimeout(() => {
      const segment = document.querySelector('ion-segment') as any;
      if (segment) {
        segment.value = 'basic';
      }
    }, 100);
  }

  /**
   * 准备提交数据
   */
  private prepareFormData(): KeyPointInfo {
    const formData = Object.assign(this.modelInfo, this.infoFormGroup.value);
    formData.isItRaining = this.convertBooleanToString(this.isItRaining);
    formData.pointGeom = this.ostMap.view.getCenter();
    return formData;
  }

  /**
   * 保存表单
   */
  private async onSubmit(formData: KeyPointInfo): Promise<void> {
    const operate = this.modelMode === DetailsMode.ADD
      ? this.netSer.addKeyPoint(formData)
      : this.netSer.updateKeyPoint(formData);

    try {
      const res = await operate.pipe(takeUntil(this.destroy$)).toPromise();
      if (res?.code === 0) {
        this.toastSer.presentToast('操作成功', 'success');
        await this.modalController.dismiss({ centerCoord: formData.pointGeom }, 'confirm');
      } else {
        this.toastSer.presentToast(res?.msg || '操作失败', 'danger');
      }
    } catch (error) {
      console.error('Error submitting form data:', error);
      this.toastSer.presentToast('网络错误，请稍后重试', 'danger');
    }
  }

  /**
   * 回退
   */
  goBack(): void {
    this.modalController.dismiss();
  }

  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton(event: CustomEvent): void {
    event.detail.register(100, async () => {
      event.stopImmediatePropagation();
      await this.modalController.dismiss();
    });
  }

  /**
   * 开始采集按钮点击
   */
  startCollect(): void {
    this.isCollecting = true;
    this.countdown = 5;
    this.collectedLocations = [];
    this.bestLocation = null;
    this.resetAccuracyStatus(); // 重置精度状态
    this.startLocationCollection();
    this.countdownTick();
  }

  /**
   * 开始定位采集
   */
  private startLocationCollection(): void {
    // 启动位置监听
    this.setupLocationListener();
    
    // 立即开始第一次定位
    this.collectLocation();
    
    // 每2秒采集一次定位，持续5秒（总共3次定位）
    const collectionInterval = setInterval(() => {
      if (this.isCollecting) {
        this.collectLocation();
      } else {
        clearInterval(collectionInterval);
      }
    }, 2000);
  }

  /**
   * 采集定位信息
   */
  private async collectLocation(): Promise<void> {
    try {
      console.log('开始采集定位...');
      
      // 直接调用地图组件中tools-bar的onLocation方法
      if (this.ostMap && this.ostMap.toolsBar) {
        console.log('使用tools-bar组件的onLocation方法');
        
        // 启动定位，但不等待完成
        this.ostMap.toolsBar.onLocation().catch(error => {
          console.error('定位过程中出错:', error);
        });
        
        // 缩短等待时间，只等待0.5秒让定位开始
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 检查是否获取到定位结果
        if (this.ostMap.currentLocationInfo && 
            this.ostMap.currentLocationInfo.longitude !== 0 && 
            this.ostMap.currentLocationInfo.latitude !== 0) {
          console.log('tools-bar定位成功:', this.ostMap.currentLocationInfo);
        } else {
          console.log('tools-bar定位未返回结果，继续监听...');
        }
      } else {
        console.log('tools-bar组件不可用');
      }
    } catch (error) {
      console.error('定位采集失败:', error);
    }
  }

  /**
   * 处理定位结果
   */
  private handleLocationResult(coordinate: Coordinate, accuracy: number): void {
    console.log('处理定位结果:', { coordinate, accuracy });
    
    const location = { coordinate, accuracy };
    this.collectedLocations.push(location);
    console.log('已采集定位数量:', this.collectedLocations.length);
    
    // 更新最佳定位点（精度最高）
    if (!this.bestLocation || accuracy < this.bestLocation.accuracy) {
      this.bestLocation = location;
      console.log('更新最佳定位点:', { coordinate, accuracy });
    } else {
      console.log('当前定位精度不如最佳定位点，跳过');
    }
  }

  /**
   * 监听地图组件的定位结果
   */
  private setupLocationListener(): void {
    console.log('启动位置监听...');
    
    // 监听地图组件的currentLocationInfo变化
    const checkLocationInfo = () => {
      if (this.ostMap && this.ostMap.currentLocationInfo) {
        const info = this.ostMap.currentLocationInfo;
        console.log('检查位置信息:', info);
        
        // 检查是否有有效的定位结果（经纬度不为0且精度大于0）
        if (info.longitude !== 0 && info.latitude !== 0 && info.accuracy > 0) {
          const coordinate: Coordinate = [info.longitude, info.latitude];
          console.log('获取到有效定位结果:', { coordinate, accuracy: info.accuracy });
          this.handleLocationResult(coordinate, info.accuracy);
        } else {
          console.log('位置信息无效:', info);
        }
      } else {
        console.log('地图组件或位置信息不可用');
      }
    };

    // 每200ms检查一次位置信息（提高频率）
    const locationInterval = setInterval(() => {
      if (this.isCollecting) {
        checkLocationInfo();
      } else {
        console.log('停止位置监听');
        clearInterval(locationInterval);
      }
    }, 200);

    // 立即检查一次
    setTimeout(checkLocationInfo, 100);
  }

  /**
   * 倒计时递减
   */
  private countdownTick(): void {
    if (this.countdown > 1) {
      setTimeout(() => {
        this.countdown--;
        this.countdownTick();
      }, 1000);
    } else {
      this.finishCollection();
    }
  }

  /**
   * 完成采集
   */
  private finishCollection(): void {
    this.isCollecting = false;

    if (this.bestLocation) {
      // 使用最佳定位点
      this.coordinate = this.bestLocation.coordinate;
      this.ostMap.setCurrentLocation(this.coordinate, this.bestLocation.accuracy);
      console.log('采集完成，最佳定位点:', this.bestLocation);

      // 评估偏差值并设置状态
      this.evaluateAccuracy(this.bestLocation.accuracy);

      // 采集成功，关闭遮罩，显示位置确认界面
      this.showMask = false;

      // 根据偏差值给出不同的提示
      if (this.accuracyStatus === 'good') {
        this.toastSer.presentToast('位置采集成功，精度良好，请确认位置', 'success');
      } else {
        this.showAccuracyTip = true;
      }
    } else {
      // 采集失败，重新显示遮罩让用户重试
      this.toastSer.presentToast('定位采集失败，请重试', 'danger');
      console.error('定位采集失败，未获取到有效定位');
      // 重置状态，允许重新采集
      this.countdown = 5;
      this.collectedLocations = [];
      this.bestLocation = null;
      this.resetAccuracyStatus();
    }
  }

  /**
   * 返回位置选择步骤
   */
  backToLocationStep(): void {
    this.currentStep = 'location';
    this.showMap = true;
    this.locationConfirmed = false;
    this.resetAccuracyStatus(); // 重置精度状态

    // 自动切换到地图tab
    this.resetSegmentToMap();
  }

  /**
   * 评估定位精度
   * @param accuracy 精度值（米）
   */
  private evaluateAccuracy(accuracy: number): void {
    // 调整精度标准：10米以内为良好，超过10米提示偏差较大
    if (accuracy <= 10) {
      this.accuracyStatus = 'good';
      this.showAccuracyTip = false;
    } else {
      this.accuracyStatus = 'poor';
      this.showAccuracyTip = true;
    }
  }

  /**
   * 重置精度状态
   */
  private resetAccuracyStatus(): void {
    this.accuracyStatus = 'unknown';
    this.showAccuracyTip = false;
  }

  /**
   * 关闭精度提示
   */
  closeAccuracyTip(): void {
    this.showAccuracyTip = false;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
