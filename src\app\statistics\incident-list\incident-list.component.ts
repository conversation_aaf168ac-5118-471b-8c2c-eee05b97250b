import { ChangeDetectorRef, Component, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailsMode } from 'src/app/@core/base/environment';
import { PageGridResult } from 'src/app/@core/base/request-result';
import { EventParams, EvReportInfo } from 'src/app/execut/class/evreport';
import { EvreportComponent } from 'src/app/execut/modal/evreport/evreport.component';
import { ExecutService } from 'src/app/execut/execut.service';
import { StatisticsService } from '../statistics.service';

@Component({
  selector: 'app-incident-list',
  templateUrl: './incident-list.component.html',
  styleUrls: ['./incident-list.component.scss']
})
export class IncidentListComponent implements OnInit, OnDestroy {
  @Input() createTime: string = '';
  @Input() startTime: string = '';
  @Input() endTime: string = '';
  // 激活tabid
  @Input() activeTabId = '管道占压';
  // 分页数据
  pageResult: PageGridResult<EvReportInfo[]>;
  // 分页列表数据
  gridData: EvReportInfo[] = [];
  // 搜索条件
  eventParams: EventParams = new EventParams();
  // 下拉刷新事件
  refreshEvent: any;
  // 上拉加载更多
  moreEvent: any;
  // 上拉到底
  isShowNoMoreStr = '加载更多...';
  spinnerType = 'bubbles';
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private modalCtrl: ModalController,
    public cd: ChangeDetectorRef,
    private netSer: StatisticsService,
    private exeSer: ExecutService,
  ) { }

  ngOnInit() {
    if (!!this.createTime) {
      this.eventParams.createTime = this.createTime;
    } else {
      this.eventParams.startTime = this.startTime;
      this.eventParams.endTime = this.endTime;
    }
    // 列表加载
    this.loadGridData(this.eventParams, this.initGridSuccess);
  }

  onSegmentChange(event) {
    this.activeTabId = event.detail.value;
    this.eventParams.eventType = event.detail.value;
    this.loadGridData(this.eventParams, this.initGridSuccess);
  }

  loadGridData(params: EventParams, functionName: (arg0: any) => void): void {
    params.eventType = this.activeTabId;
    this.netSer.getEventList(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe(functionName, this.initGridError);
  }

  // 列表加载成功
  initGridSuccess = (result: PageGridResult<EvReportInfo[]>) => {
    this.pageResult = result;
    this.gridData = result.data.records; // 赋值
    this.closeRefresh(); // 关闭刷新
    this.cd.markForCheck();
  }
  // 下拉加载更多
  moreGridSuccess = (result: PageGridResult<EvReportInfo[]>) => {
    this.gridData = this.gridData.concat(result.data.records);
    this.moreEvent.target.complete();
    this.cd.markForCheck();
  }
  // 列表加载失败
  initGridError = () => {
    this.closeRefresh();
  }


  /**
   * 下拉刷新
   */
  doRefresh(event): void {
    this.refreshEvent = event;
    this.eventParams = new EventParams();
    this.eventParams.createTime = this.createTime;
    this.eventParams.startTime = this.startTime;
    this.eventParams.endTime = this.endTime;
    this.loadGridData(this.eventParams, this.initGridSuccess);
  }

  // 关闭刷新
  closeRefresh(): void {
    if (this.refreshEvent) {
      this.refreshEvent.target.complete();
    }
  }

  /**
   * 重置
   */
  onClearInput(): void {
    if (this.eventParams.eventName == '') {
      this.eventParams = new EventParams();
      this.eventParams.eventType = this.activeTabId;
      if (!!this.createTime) {
        this.eventParams.createTime = this.createTime;
      } else {
        this.eventParams.startTime = this.startTime;
        this.eventParams.endTime = this.endTime;
      }
      this.loadGridData(this.eventParams, this.initGridSuccess);
    }
  }
  /**
   * 搜索
   */
  onSearch(): void {
    this.eventParams.pageNum = 1;
    this.loadGridData(this.eventParams, this.initGridSuccess);
  }

  getDetailInfo(info: EvReportInfo) {
    this.exeSer.getEReportByCode({ eventCode: info.eventCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe((ret) => {
        this.openEventReport(ret.data);
      });
  }

  /**
   * 打开事件上报
   */
  async openEventReport(modelInfo): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: EvreportComponent,
      componentProps: { modelInfo, modelMode: DetailsMode.SEE },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data === 'refresh') {
      this.loadGridData(this.eventParams, this.initGridSuccess);
    }
  }

  /**
   * 上拉加载更多
   */
  loadData(event): void {
    if (this.pageResult.data.total > (this.eventParams.pageNum * this.pageResult.data.pageSize)) {
      this.moreEvent = event;
      this.eventParams.pageNum = this.eventParams.pageNum + 1;
      this.loadGridData(this.eventParams, this.moreGridSuccess);
    } else {
      this.spinnerType = null;
      this.isShowNoMoreStr = '到底啦~';
      setTimeout(() => {
        event.target.complete();
      }, 1000);
    }
  }

  /**
   * 获取事件类型样式类
   */
  getEventTypeClass(eventType: string): string {
    const typeClassMap = {
      '管道占压': 'type-pipeline-pressure',
      '设备失效': 'type-equipment-failure',
      '第三方施工': 'type-third-party',
      '隐患上报': 'type-hidden-danger'
    };
    return typeClassMap[eventType] || 'type-default';
  }

  /**
   * 获取事件状态样式类
   */
  getStatusClass(status: string): string {
    const statusClassMap = {
      '未消除': 'status-uncleared',
      '消除': 'status-cleared'
    };
    return statusClassMap[status] || 'status-default';
  }

  /**
   * 格式化时间显示
   */
  formatTime(timeStr: string): string {
    if (!timeStr) return '';

    const date = new Date(timeStr);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // 今天
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      // 昨天
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays < 7) {
      // 一周内
      return `${diffDays}天前`;
    } else {
      // 超过一周
      return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
    }
  }

  /**
   * 获取事件优先级（用于排序）
   */
  getEventPriority(eventType: string): number {
    const priorityMap = {
      '设备失效': 1,
      '管道占压': 2,
      '第三方施工': 3,
      '隐患上报': 4
    };
    return priorityMap[eventType] || 5;
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

  /**
   * 回退
   */
  goBack(): void {
    this.modalCtrl.dismiss();
  }
  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event: any): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalCtrl.dismiss();
    });
  }

}
