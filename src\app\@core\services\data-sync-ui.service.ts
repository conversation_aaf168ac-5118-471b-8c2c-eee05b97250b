import { Injectable } from '@angular/core';
import { AlertController, ActionSheetController, Platform } from '@ionic/angular';
import { DataSyncManagerService } from '../providers/data-sync';
import { ToastService } from '../providers/toast.service';
import { AndroidPermissions } from '@ionic-native/android-permissions/ngx';
import { File } from '@ionic-native/file/ngx';

/**
 * 数据同步UI管理服务
 * 负责处理数据同步相关的用户界面交互
 */
@Injectable({
  providedIn: 'root'
})
export class DataSyncUiService {

  constructor(
    private dataSyncManager: DataSyncManagerService,
    private toastService: ToastService,
    private alertCtrl: AlertController,
    private actionSheetCtrl: ActionSheetController,
    private platform: Platform, private file: File,
    private androidPermissions: AndroidPermissions,
  ) { }

  /**
   * 检查失败数据数量
   */
  async getFailedDataCount(): Promise<number> {
    try {
      const failedData = await this.dataSyncManager.getFailedData();
      return failedData.reduce((total, item) => total + item.items.length, 0);
    } catch (error) {
      console.error('检查失败数据数量时出错:', error);
      return 0;
    }
  }

  /**
   * 打开数据同步管理界面
   */
  async openDataSyncManager(): Promise<void> {
    const failedDataCount = await this.getFailedDataCount();

    if (failedDataCount === 0) {
      await this.toastService.presentToast('当前没有需要处理的失败数据', 'success', 2000, 'middle');
      return;
    }

    const actionSheet = await this.actionSheetCtrl.create({
      header: `数据同步管理 (${failedDataCount}条失败数据)`,
      buttons: [
        {
          text: '手动重试上传',
          icon: 'refresh-outline',
          handler: () => {
            this.manualRetryUpload();
          }
        },
        {
          text: '导出巡检离线数据',
          icon: 'download-outline',
          handler: () => {
            this.exportFailedData();
          }
        },
        // {
        //   text: '查看详细信息',
        //   icon: 'information-circle-outline',
        //   handler: () => {
        //     this.showFailedDataDetails();
        //   }
        // },
        {
          text: '取消',
          icon: 'close',
          role: 'cancel'
        }
      ]
    });
    await actionSheet.present();
  }

  /**
   * 手动重试上传
   */
  async manualRetryUpload(): Promise<void> {
    const loading = await this.alertCtrl.create({
      message: '正在重试上传...',
      backdropDismiss: false
    });
    await loading.present();

    try {
      await this.dataSyncManager.manualRetryFailedData();
      await loading.dismiss();

      // 等待一段时间后检查结果
      setTimeout(async () => {
        const failedDataCount = await this.getFailedDataCount();
        const message = failedDataCount === 0
          ? '所有数据上传成功！'
          : `还有 ${failedDataCount} 条数据上传失败`;

        await this.toastService.presentToast(
          message,
          failedDataCount === 0 ? 'success' : 'warning',
          3000,
          'middle'
        );
      }, 2000);

    } catch (error) {
      await loading.dismiss();
      await this.toastService.presentToast('重试上传失败，请检查网络连接', 'danger', 3000, 'middle');
    }
  }

  /**
   * 导出失败数据
   */
  async exportFailedData(): Promise<void> {
    try {
      const exportContent = await this.dataSyncManager.exportFailedDataToFile();

      if (exportContent === '没有失败的数据需要导出') {
        await this.toastService.presentToast(exportContent, 'success', 2000, 'middle');
        return;
      }

      // 在安卓环境中写入文件
      await this.writeToFile(exportContent);

    } catch (error) {
      console.error('导出数据失败:', error);
      await this.toastService.presentToast('导出数据失败，请稍后重试', 'danger', 3000, 'middle');
    }
  }

  /**
   * 显示失败数据详细信息
   */
  async showFailedDataDetails(): Promise<void> {
    try {
      const failedData = await this.dataSyncManager.getFailedData();

      let message = '失败数据详情:\n\n';
      failedData.forEach(({ type, items }) => {
        message += `${type}: ${items.length}条\n`;
        items.forEach(item => {
          message += `  - ID: ${item.id.substring(0, 8)}...\n`;
          message += `  - 重试次数: ${item.retryCount}\n`;
          message += `  - 时间: ${new Date(item.timestamp).toLocaleString()}\n\n`;
        });
      });

      const alert = await this.alertCtrl.create({
        header: '失败数据详情',
        message,
        buttons: ['确定']
      });
      await alert.present();

    } catch (error) {
      console.error('获取失败数据详情时出错:', error);
    }
  }

  /**
   * 在安卓环境中写入文件 (优化版)
   * - 使用 async/await 和 Promise
   * - 包含运行时权限检查
   * - 写入到用户可访问的公共下载目录
   */
  async writeToFile(content: string): Promise<void> {
    // 仅在 Cordova 环境 (真机/模拟器) 下执行
    if (!this.platform.is('cordova')) {
      console.warn('文件写入功能仅在 Cordova 环境下可用');
      await this.toastService.presentToast('文件写入功能仅在浏览器中不可用', 'warning');
      return;
    }

    try {
      // 1. 检查并请求权限
      const hasPermission = await this.checkAndRequestPermission();
      if (!hasPermission) {
        console.error('用户未授予写入权限');
        await this.toastService.presentToast('写入文件失败：未获得权限', 'danger');
        return;
      }

      // 2. 确定文件路径和文件名
      // externalRootDirectory 指向设备存储的根目录
      const directory = this.file.externalRootDirectory + 'Download/';
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
      const fileName = `巡检离线数据_${timestamp}.txt`;

      // 3. 创建 Blob 对象
      const blob = new Blob([content], { type: 'text/plain' });

      // 4. 写入文件 (checkFile 会检查目录是否存在)
      // 使用 `writeFile` 函数，它会处理创建和写入的整个流程
      await this.file.writeFile(directory, fileName, blob, { replace: true });

      const filePath = `${directory}${fileName}`;
      console.log(`文件成功写入到: ${filePath}`);
      await this.toastService.presentToast(`数据已导出到 "下载" 文件夹: ${fileName}`, 'success', 4000);

    } catch (error) {
      console.error('文件写入过程中发生错误:', error);
      // 处理不同类型的错误
      if (error.code === 1 /* NOT_FOUND_ERR */) {
        await this.toastService.presentToast('写入失败: 路径不存在', 'danger');
      } else {
        await this.toastService.presentToast('文件操作失败', 'danger');
      }
    }
  }

  /**
   * 检查并请求安卓写入权限
   */
  private async checkAndRequestPermission(): Promise<boolean> {
    try {
      // 检查当前是否已有权限
      const checkResult = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE);
      if (checkResult.hasPermission) {
        return true;
      }

      // 如果没有，则请求权限
      const requestResult = await this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE);
      return requestResult.hasPermission;
    } catch (error) {
      console.error('请求权限时出错:', error);
      return false;
    }
  }
}
