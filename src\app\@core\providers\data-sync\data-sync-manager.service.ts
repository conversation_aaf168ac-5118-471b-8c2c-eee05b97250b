import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

import { NetworkService } from '../network.service';
import { SyncCacheManagerService } from './sync-cache-manager.service';
import { SyncUploadManagerService } from './sync-upload-manager.service';
import { SyncRetryManagerService } from './sync-retry-manager.service';
import { 
  SyncDataType, 
  SyncCacheItem, 
  AddToCacheResult, 
  FailedDataGroup 
} from './types/sync-data.types';

// 重新导出类型，保持向后兼容
export { SyncDataType, SyncCacheItem } from './types/sync-data.types';

/**
 * 统一数据同步服务（主协调器）
 * 负责协调各个子服务，提供统一的对外接口
 * 注：数据加密由CoreInterceptorService统一处理
 */
@Injectable({
  providedIn: 'root'
})
export class DataSyncManagerService {
  /**
   * 网络状态（true为在线，false为离线）
   */
  private networkStatus$ = new BehaviorSubject<boolean>(false);

  constructor(
    private networkService: NetworkService,
    private cacheManager: SyncCacheManagerService,
    private uploadManager: SyncUploadManagerService,
    private retryManager: SyncRetryManagerService
  ) { }

  /**
   * 初始化网络状态监听和自动上传逻辑，需在 deviceready 后调用
   */
  public init() {
    this.networkService.getNetworkStatus().subscribe(isOnline => {
      console.log(`[DataSyncManagerService] Network status changed: ${isOnline ? 'Online' : 'Offline'}`);
      this.networkStatus$.next(isOnline);
      if (isOnline && !this.uploadManager.isCurrentlyUploading()) {
        this.uploadAllCachedData();
      }
    });
  }

  /**
   * 添加业务数据到本地缓存
   * 返回本次数据的实际处理结果
   */
  async addToCache(type: SyncDataType, data: any, uploadUrl: string, method: string = 'POST'): Promise<AddToCacheResult> {
    // 先添加到缓存
    const cacheItem = await this.cacheManager.addToCache(type, data, uploadUrl, method);

    // 尝试立即上传
    try {
      const uploadResult = await this.uploadManager.tryImmediateUpload(type, data, uploadUrl, method);
      
      if (uploadResult.success) {
        // 上传成功，从缓存中移除
        await this.cacheManager.removeFromCache(type, cacheItem.id);
        return { 
          status: 'uploaded', 
          code: uploadResult.code || 0, 
          msg: uploadResult.msg || '上传成功' 
        };
      } else {
        return { status: 'cached' };
      }
    } catch (err) {
      console.error('[DataSyncManagerService] 立即上传失败，数据已缓存:', err);
      return { status: 'cached' };
    }
  }

  /**
   * 获取指定类型的本地缓存数据
   */
  async getCache(type: SyncDataType): Promise<SyncCacheItem[]> {
    return this.cacheManager.getCache(type);
  }

  /**
   * 移除指定类型的缓存项
   */
  async removeFromCache(type: SyncDataType, id: string): Promise<void> {
    return this.cacheManager.removeFromCache(type, id);
  }

  /**
   * 批量上传所有业务类型的本地缓存数据
   */
  async uploadAllCachedData(): Promise<void> {
    return this.uploadManager.uploadAllCachedData();
  }

  /**
   * 获取所有达到最大重试次数的失败数据
   */
  async getFailedData(): Promise<FailedDataGroup[]> {
    return this.retryManager.getFailedData();
  }

  /**
   * 手动重试失败的数据（重置重试次数）
   */
  async manualRetryFailedData(): Promise<void> {
    await this.retryManager.manualRetryFailedData();
    
    // 触发重新上传
    if (this.networkStatus$.value) {
      this.uploadAllCachedData();
    }
  }

  /**
   * 导出失败数据到文本文件
   */
  async exportFailedDataToFile(): Promise<string> {
    return this.retryManager.exportFailedDataToText();
  }

  // ========== 扩展方法（可选使用） ==========

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    return this.cacheManager.getCacheStats();
  }

  /**
   * 获取重试统计信息
   */
  async getRetryStats() {
    return this.retryManager.getRetryStats();
  }

  /**
   * 获取失败数据数量
   */
  async getFailedDataCount(): Promise<number> {
    return this.retryManager.getFailedDataCount();
  }

  /**
   * 清理失败数据
   */
  async clearFailedData(): Promise<number> {
    return this.retryManager.clearFailedData();
  }

  /**
   * 检查是否正在上传
   */
  isCurrentlyUploading(): boolean {
    return this.uploadManager.isCurrentlyUploading();
  }

  /**
   * 获取所有缓存数据
   */
  async getAllCache() {
    return this.cacheManager.getAllCache();
  }

  /**
   * 清空指定类型的缓存
   */
  async clearCache(type: SyncDataType): Promise<void> {
    return this.cacheManager.clearCache(type);
  }

  /**
   * 清空所有缓存
   */
  async clearAllCache(): Promise<void> {
    return this.cacheManager.clearAllCache();
  }
}
