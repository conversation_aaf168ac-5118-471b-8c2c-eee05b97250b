import { Component, EventEmitter, Input, <PERSON>Zone, OnDestroy, OnInit, Output } from '@angular/core';
import { BackgroundGeolocation, BackgroundGeolocationEvents, BackgroundGeolocationResponse, ServiceStatus } from '@ionic-native/background-geolocation/ngx';
import { Feature } from 'ol';
import GeoJSON from 'ol/format/GeoJSON';
import Geometry from 'ol/geom/Geometry';
import LineString from 'ol/geom/LineString';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Fill, Stroke, Style } from 'ol/style';
import { Subject, Subscription, from } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ShareMethodService } from 'src/app/@core/providers/share-method.service';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { NetworkService } from 'src/app/@core/providers/network.service';
import { MapComponent } from 'src/app/share/map-component/map/map.component';
import { Task } from 'src/app/work/class/work';
import { WorkService } from 'src/app/work/work.service';
import { TaskInfo } from '../class/task.bean';
import { ExecutState } from '../execut.component';
import { ExecutService } from '../execut.service';
import { CurrentLocation, LocationInfo } from '../class/key-point';
import { PageEventService } from 'src/app/home/<USER>';
import { LocationService } from '../services/location.service';

@Component({
  selector: 'task-control-bar',
  templateUrl: './control-bar.component.html',
  styleUrls: ['./control-bar.component.scss']
})
export class ControlBarComponent implements OnInit, OnDestroy {
  @Input() task: Task;
  @Input() currentTime: string;
  @Input() mapCmpt: MapComponent;
  @Input() executState: ExecutState = 'create';
  // 事件上报
  @Output() evReportClick: EventEmitter<[number, number]> = new EventEmitter();
  // 关键点
  @Output() keyPointClick: EventEmitter<[number, number]> = new EventEmitter();
  // 手动打卡
  @Output() clockInClick: EventEmitter<void> = new EventEmitter();
  KeyPointLayer: VectorLayer<VectorSource<Geometry>>;
  // 当前位置信息
  currentLocation: LocationInfo = new CurrentLocation();
  // 上一次轨迹点
  nextTrackCoordinates: number[][] = [];
  // 控制条展示
  controlBarShowState = true;
  // 视野跟随
  viewFollow = true;
  // 第一次定位
  firstLocation = true;
  // 位置监听
  location$: Subscription;
  // 前台监听
  foreground$: Subscription;
  // 执行状态
  trackRunState: TrackRunState = 'notstart';
  // 轨迹线
  trackLineFeature: Feature<LineString> = new Feature();
  // 轨迹图册
  trackLayer: VectorLayer<VectorSource<Geometry>> = new VectorLayer({ source: new VectorSource() });
  // 轨迹时间
  trackTime: any = '00:00:00';
  // 计时器
  timer: any;
  // 任务开始时间
  startTime: number;
  // 任务结束时间
  endTime: number;
  subTimes = 0;
  difference = 0;
  // 显示开始提醒
  showStartMsg = false;
  // 结束长按按钮
  ringRate = 0;
  // 显示结束窗口
  showRing = false;
  // 当前日期
  nowDate = this.shareUtils.dateTransform(new Date(), 'yyyy-MM-dd');
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  constructor(
    private ngZone: NgZone, private shareUtils: ShareMethodService,
    private userSer: UserInfoService, private dataEvent: PageEventService,
    private exeSer: ExecutService, private trajectorySer: WorkService,
    private toastSer: ToastService, private networkService: NetworkService,
    public backgroundGeolocation: BackgroundGeolocation,
    private locationService: LocationService,
  ) { }

  async ngOnInit(): Promise<void> {
    // 初始化轨迹要素
    this.initTrackFeature();
    // 添加轨迹图层
    this.mapCmpt.map.addLayer(this.trackLayer);
    // 注册位置监听
    this.location$ = this.backgroundGeolocation.on(BackgroundGeolocationEvents.location)
      .subscribe(this.onLocationChange);
    // 注册前台监听
    this.foreground$ = this.backgroundGeolocation.on(BackgroundGeolocationEvents.foreground)
      .subscribe(this.onForegroundChange);
    // 检查是否存在历史轨迹
    this.checkExecutState();
  }

  async loadKeyPointLayer(): Promise<void> {
    if (!this.KeyPointLayer) { // 找出关键点图层
      this.KeyPointLayer = this.mapCmpt.map.getLayers().getArray().find(i => i.getClassName() === 'key-point') as VectorLayer<VectorSource<Geometry>>;
    }
  }

  /**
   * 事件上报
   */
  onEvReportClick() {
    this.evReportClick.emit([this.currentLocation.longitude, this.currentLocation.latitude]);
  }

  /**
   * 关键点
   */
  onKeyPointClick() {
    this.keyPointClick.emit([this.currentLocation.longitude, this.currentLocation.latitude]);
  }

  /**
   * 手动打卡
   */
  onClockInClick() {
    this.clockInClick.emit();
  }

  /**
   * 检查是否存在历史轨迹
   */
  checkExecutState(): void {
    // 如果不是新任务则加载历史轨迹
    if (this.executState === 'continue') {
      this.loadHistoryTrack(this.task.taskCode);
      this.currentTime = this.task.groupCode;
    }
  }

  /**
   * 加载历史轨迹
   * @param taskCode 任务编码
   */
  loadHistoryTrack(taskCode: string) {
    this.trajectorySer.getTrailPointsByTask({ taskCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe((ret) => {
        // 筛选当前用户的轨迹数据
        const currentUserTrack = ret.data.find(item => item.userName === this.userSer.userName);
        if (!currentUserTrack) {
          this.toastSer.presentToast('未找到当前用户历史轨迹数据', 'danger');
          return;
        }

        const points = new GeoJSON().readFeatures(currentUserTrack.geom);
        this.nextTrackCoordinates = points.map(p => p.getGeometry().getCoordinates());
        this.trackLineFeature.getGeometry().setCoordinates(this.nextTrackCoordinates);
      });
  }

  ngOnDestroy(): void {
    // 清理定时器，防止内存泄漏
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    this.trackLayer.getSource().clear();
    this.mapCmpt.map.removeLayer(this.trackLayer);
    this.location$?.unsubscribe();
    this.foreground$?.unsubscribe();

    // 清理 destroy$ Subject
    this.destroy$.next();
    this.destroy$.complete();

    // 确保停止后台定位服务
    this.locationService.stopBackgroundGeolocation();
    console.log('🛑 Control-bar 组件销毁，后台定位服务已停止');
  }

  /**
   * 位置监听
   */
  onLocationChange = (location: BackgroundGeolocationResponse) => {
    this.ngZone.run(async () => {
      await this.loadKeyPointLayer();
      this.showStartMsg = false;
      if (this.firstLocation) {
        const firstLocationInfo = this.createTrackInfo(location);

        if (this.networkService.isOnline()) { // 如果有网络
          this.uploadTrackInfo(firstLocationInfo);
        }
      }
      this.updateCurrentLocation(location);
      this.firstLocation = false;
    });
  }

  createTrackInfo(location: BackgroundGeolocationResponse): TaskInfo {
    return {
      altitude: location.altitude,
      latitude: location.latitude,
      trajectoryTime: location.time,
      userName: this.userSer.userName,
      userCode: this.userSer.userId,
      depCode: this.userSer.depCode,
      depName: this.userSer.depName,
      speed: location.speed,
      longitude: location.longitude,
      taskCode: this.task.taskCode,
      groupCode: this.currentTime,
      accuracy: location.accuracy,
    };
  }

  /**
   * 上传轨迹信息
   */
  uploadTrackInfo(firstLocationInfo: TaskInfo): void {
    this.exeSer.uploadTrack([firstLocationInfo])
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => { });
    this.exeSer.startTasck(firstLocationInfo)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => { });
  }

  /**
   * 更新当前位置
   */
  updateCurrentLocation(location: BackgroundGeolocationResponse): void {
    // 验证位置数据是否有效
    if (!location || typeof location.longitude !== 'number' || typeof location.latitude !== 'number') {
      console.warn('updateCurrentLocation: 无效的位置数据', location);
      return;
    }

    this.currentLocation = {
      longitude: location.longitude || 0,
      latitude: location.latitude || 0,
      speed: location.speed || 0,
      accuracy: location.accuracy || 0,
      trajectoryTime: location.time
    };

    const coordinate = [location.longitude, location.latitude];
    this.trackLineFeature.getGeometry().appendCoordinate(coordinate);
    this.mapCmpt.setCurrentLocation(coordinate, location.accuracy, this.viewFollow);
  }

  /**
   * 切换前台监听
   */
  onForegroundChange = () => {
    this.ngZone.run(async () => {
      const locations = await this.backgroundGeolocation.getLocations() as BackgroundGeolocationResponse[];
      const allCoordinates = this.nextTrackCoordinates.concat(locations.map(item => [item.longitude, item.latitude]));
      this.trackLineFeature.getGeometry().setCoordinates(allCoordinates);

      // 获取最后一个坐标并验证其有效性
      const lastCoordinate = this.trackLineFeature.getGeometry().getLastCoordinate();
      if (lastCoordinate && Array.isArray(lastCoordinate) && lastCoordinate.length >= 2) {
        this.mapCmpt.setCurrentLocation(lastCoordinate, 0, this.viewFollow);
      } else {
        console.warn('onForegroundChange: 无法获取有效的最后坐标', lastCoordinate);
      }
    });
  }

  /**
   * 创建轨迹图层
   */
  initTrackFeature(): void {
    // 轨迹线样式
    const trackLineStyle = new Style({
      fill: new Fill({
        color: '#3CC777',
      }),
      stroke: new Stroke({
        color: '#3CC777',
        width: 3
      })
    });
    this.trackLineFeature.setStyle(trackLineStyle);
    this.trackLineFeature.setGeometry(new LineString([]));
    this.trackLayer.getSource().addFeature(this.trackLineFeature);
  }

  /**
   * 开始任务
   */
  async onPlayClick(): Promise<void> {
    try {
      this.trackRunState = 'running';
      this.showStartMsg = true;
      this.openTiming();

      // 使用LocationService启动定位，确保使用正确的配置
      await this.locationService.startBackgroundGeolocation();
      console.log('✅ 任务开始，定位服务已启动');
    } catch (error) {
      console.error('❌ 启动定位服务失败:', error);
      this.toastSer.presentToast('启动定位服务失败，请检查定位权限', 'danger');
      this.trackRunState = 'notstart';
      this.showStartMsg = false;
    }
  }

  /**
   * 停止任务
   */
  onStopClick(): void {
    this.trackRunState = 'pause';
    this.showStartMsg = false;
    this.subTimes = this.difference;
    clearInterval(this.timer);
    this.locationService.stopBackgroundGeolocation();
  }
  /**
   * 结束任务
   */
  async finishClick(): Promise<void> {
    // 清空计时器
    clearInterval(this.timer);
    // 隐藏结束动画
    this.ringRate = 0;
    this.showRing = false;
    // 关闭初始化信息
    this.showStartMsg = false;
    // 移动视野到全部
    const coordinates = this.trackLineFeature.getGeometry().getCoordinates();
    if (coordinates.length > 0) {
      try {
        const extent = this.trackLineFeature.getGeometry().getExtent();
        // 验证范围是否有效
        if (extent && extent.length === 4 &&
          Number.isFinite(extent[0]) && Number.isFinite(extent[1]) &&
          Number.isFinite(extent[2]) && Number.isFinite(extent[3]) &&
          extent[0] < extent[2] && extent[1] < extent[3]) {
          this.mapCmpt.view.fit(extent, { padding: [50, 50, 50, 50], duration: 500, maxZoom: 15 });
        } else {
          console.warn('finishClick: 轨迹范围无效，跳过视图适配', extent);
        }
      } catch (error) {
        console.error('finishClick: 适配轨迹视图时发生错误', error);
      }
    }
    // 关闭定位服务
    this.locationService.stopBackgroundGeolocation();
    // 强制同步
    // 提示数据同步中
    this.backgroundGeolocation.forceSync().then();
    this.exeSer.endTasck({ taskCode: this.task.taskCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        // 切换状态为关闭
        this.trackRunState = 'stop';
      }, () => {
        // 切换状态为关闭
        this.trackRunState = 'stop';
      });
  }

  onFinishStartPress(): void {
    this.showRing = true;
    this.showStartMsg = false;
  }

  onFinishStopPress(): void {
    this.ringRate = 0;
    this.showRing = false;
    from(this.backgroundGeolocation.checkStatus()).subscribe((serviceStatus: ServiceStatus) => {
      this.showStartMsg = serviceStatus.isRunning;
    });
  }

  onFinishPressing($event: any): void {
    if (this.ringRate === 100) {
      // 关闭服务
      this.finishClick();
      this.dataEvent.sendData('refreshCompletionRate', true);
    }
    this.ringRate = $event / 1 * 100;
  }

  /**
   * 开启计时器
   */
  openTiming(): void {
    this.startTime = new Date().getTime();
    const count = () => {
      this.ngZone.run(() => {
        this.difference = Math.round((new Date().getTime() - this.startTime)) + this.subTimes;
        // tslint:disable-next-line:radix
        const h = parseInt('' + (this.difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        // tslint:disable-next-line:radix
        const m = parseInt('' + (this.difference % (1000 * 60 * 60)) / (1000 * 60));
        // tslint:disable-next-line:radix
        const s = parseInt('' + (this.difference % (1000 * 60)) / 1000);
        this.trackTime = `${h > 9 ? h : '0' + h}:${m > 9 ? m : '0' + m}:${s > 9 ? s : '0' + s}`;
      });
    };
    this.timer = setInterval(count, 1000);
  }

}

declare type TrackRunState = 'pause' | 'stop' | 'running' | 'notstart';

