<ion-header>
  <ion-toolbar>
    <ion-searchbar 
      placeholder="根据关键点名称搜索"
      (ionChange)="filterItems($event)"
      debounce="500"
      class="searchbar">
    </ion-searchbar>
    <ion-buttons slot="end" *ngIf="showClose">
      <ion-button (click)="close()">
        关闭
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content style="height: calc(60vh - 58px);">
  <ng-container *ngIf="!loading; else loadingTpl">
    <ion-grid>
      <ion-row class="ion-text-center header">
        <ion-col size="1" class="index-col">序号</ion-col>
        <ion-col class="point-name">关键点名称</ion-col>
        <ion-col class="state-col">雨天不巡</ion-col>
        <ion-col class="state-col">状态</ion-col>
        <ion-col class="data-status-col" *ngIf="showDataStatus">数据</ion-col>
      </ion-row>
      <ion-row  class="ion-text-center row"
        *ngFor="let item of keyPoints; let i = index"
        (click)="!disabledClick && onClick(item)">
        <ion-col size="1" class="index-col">{{i + 1}}</ion-col>
        <ion-col class="point-name" [ngClass]="{'state-done': item.state === '已巡'}">{{item.pointName}}</ion-col>
        <ion-col class="state-col">{{item.isItRaining}}</ion-col>
        <ion-col class="state-col" [ngClass]="{'state-done': item.state === '已巡'}">{{item.state}}</ion-col>
        <ion-col class="data-status-col" *ngIf="showDataStatus">
          <!-- 优先显示打卡数据图标 -->
          <ion-icon
            *ngIf="hasPicCode(item)"
            name="camera"
            [color]="'success'"
            class="data-icon clickable"
            (click)="onDataStatusClick(item, $event, 'clockIn')"
            title="查看打卡数据">
          </ion-icon>
          <!-- 只有在没有打卡数据时才显示未巡检原因图标 -->
          <ion-icon
            *ngIf="hasReason(item) && !hasPicCode(item)"
            name="alert-circle"
            [color]="'warning'"
            class="data-icon clickable"
            (click)="onDataStatusClick(item, $event, 'reason')"
            title="查看未巡检原因">
          </ion-icon>
          <!-- 无任何数据时显示 - -->
          <span *ngIf="!hasPicCode(item) && !hasReason(item)" class="no-data">-</span>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ng-container>
  <ng-template #loadingTpl>
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <div>加载中...</div>
    </div>
  </ng-template>
</ion-content>