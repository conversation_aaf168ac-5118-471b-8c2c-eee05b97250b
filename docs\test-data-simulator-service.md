# TestDataSimulatorService 使用文档

## 概述

`TestDataSimulatorService` 是一个专门用于生成模拟同步失败数据的服务，主要用于测试数据导出功能。该服务将原本在页面组件中的复杂逻辑提取出来，形成独立的、可复用的服务。

> 📋 **测试人员请参考**: [数据导出功能测试指南](./data-export-test-guide.md) - 完整的测试步骤和验证要点

## 服务特性

### ✅ 优势
- **职责单一**: 专门负责测试数据的生成和管理
- **易于复用**: 可在多个组件中使用
- **便于测试**: 独立的服务更容易进行单元测试
- **代码清晰**: 将复杂的数据生成逻辑从UI组件中分离

### 🔧 功能特性
- 自动清理现有失败数据
- 生成多种类型的模拟数据
- 正确设置重试次数以模拟失败状态
- 提供详细的日志输出
- 支持回调函数进行状态更新

## API 文档

### 主要方法

#### `simulateFailedSyncData(userInfo, onDataCountUpdate?)`

生成模拟的同步失败数据。

**参数**:
- `userInfo: { userId: string; depCode: string }` - 用户信息对象
- `onDataCountUpdate?: () => Promise<void>` - 可选的数据数量更新回调函数

**返回值**: `Promise<void>`

**示例**:
```typescript
await this.testDataSimulator.simulateFailedSyncData(
  { userId: 'user123', depCode: 'dept456' },
  () => this.checkFailedDataCount()
);
```

#### `clearAllFailedData()`

清空所有类型的失败数据。

**返回值**: `Promise<void>`

**示例**:
```typescript
await this.testDataSimulator.clearAllFailedData();
```

### 私有方法

#### `generateMockData(userInfo, currentTime)`

生成模拟数据，包括手动打卡、自动关键点打卡和巡检事件上报数据。

#### `createFailedCacheItems(mockData, currentTime, maxRetryCount)`

根据模拟数据创建失败的缓存项。

#### `addFailedDataToCache(failedItems, maxRetryCount)`

将失败数据添加到缓存中。

#### `logSimulationResults(mockData, failedItems)`

记录模拟结果到控制台。

## 使用指南

### 1. 服务注入

在需要使用的组件中注入服务：

```typescript
import { TestDataSimulatorService } from '../@core/services/test-data-simulator.service';

@Component({
  selector: 'app-example',
  templateUrl: './example.page.html'
})
export class ExamplePage {
  constructor(
    private testDataSimulator: TestDataSimulatorService
  ) {}
}
```

### 2. 基本使用

```typescript
async simulateData() {
  const userInfo = {
    userId: this.userService.userId,
    depCode: this.userService.depCode
  };

  await this.testDataSimulator.simulateFailedSyncData(userInfo);
}
```

### 3. 带回调的使用

```typescript
async simulateDataWithCallback() {
  const userInfo = {
    userId: this.userService.userId,
    depCode: this.userService.depCode
  };

  await this.testDataSimulator.simulateFailedSyncData(
    userInfo,
    async () => {
      // 更新UI中的失败数据数量显示
      await this.updateFailedDataCount();
    }
  );
}
```

## 生成的模拟数据

### 手动打卡数据 (2条)
```typescript
{
  taskCode: 'TASK_001',
  userCode: userInfo.userId,
  depCode: userInfo.depCode,
  longitude: 108.123456,
  latitude: 34.567890,
  clockInTime: currentTime - 3600000, // 1小时前
  clockInType: 'manual',
  remark: '手动打卡测试数据1'
}
```

### 自动关键点打卡数据 (3条)
```typescript
{
  taskCode: 'TASK_001',
  userCode: userInfo.userId,
  depCode: userInfo.depCode,
  longitude: 108.345678,
  latitude: 34.789012,
  trajectoryTime: currentTime - 1800000, // 30分钟前
  keyPointName: '关键点A',
  autoClockIn: true
}
```

### 巡检事件上报数据 (2条)
```typescript
{
  taskCode: 'TASK_001',
  userCode: userInfo.userId,
  depCode: userInfo.depCode,
  eventType: 'equipment_abnormal',
  eventLevel: 'high',
  eventDescription: '设备异常测试事件1',
  longitude: 108.678901,
  latitude: 34.012345,
  reportTime: currentTime - 2700000, // 45分钟前
  images: ['image1.jpg', 'image2.jpg'],
  attachments: []
}
```

## 错误处理

服务内置了完善的错误处理机制：

```typescript
try {
  await this.testDataSimulator.simulateFailedSyncData(userInfo);
} catch (error) {
  // 服务会自动显示错误提示
  // 错误信息会记录到控制台
  console.error('模拟数据失败:', error);
}
```

## 依赖关系

该服务依赖以下服务：
- `SyncCacheManagerService` - 缓存管理
- `ToastService` - 消息提示

## 配置说明

### 重试次数配置
```typescript
const maxRetryCount = 5; // 与系统最大重试次数保持一致
```

### 时间间隔配置
- 手动打卡数据：每个间隔30分钟
- 自动关键点打卡数据：每个间隔20分钟
- 巡检事件上报数据：每个间隔15分钟

## 最佳实践

### 1. 在测试环境中使用
```typescript
if (!environment.production) {
  await this.testDataSimulator.simulateFailedSyncData(userInfo);
}
```

### 2. 结合数据导出功能测试
```typescript
// 1. 生成模拟数据
await this.testDataSimulator.simulateFailedSyncData(userInfo);

// 2. 测试导出功能
await this.dataSyncUiService.exportFailedData();
```

### 3. 清理测试数据
```typescript
// 测试完成后清理数据
await this.testDataSimulator.clearAllFailedData();
```

## 扩展指南

如果需要添加新的数据类型，可以在以下方法中扩展：

1. `generateMockData()` - 添加新的模拟数据
2. `createFailedCacheItems()` - 创建对应的缓存项
3. `logSimulationResults()` - 更新日志输出

## 注意事项

1. **仅用于测试**: 该服务仅用于开发和测试环境
2. **数据清理**: 每次运行会自动清空现有失败数据
3. **用户信息**: 需要提供有效的用户ID和部门代码
4. **网络状态**: 不依赖网络状态，直接操作本地缓存
