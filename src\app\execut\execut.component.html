<ost-map #ostMap
  [center]="centerPoint"
  [taskCode]="task.taskCode"
  [addMapClickEvent]="mapClick"
  [layerIds]="layerIds"
  [inspectionMethod]="task.inspectionMethod"
  [showLocationProviderButton]="false"
>
  <ost-layout-item align="top-left" *ngIf="isModal">
    <div class="map-close-btn"
      [hidden]="trackBar.trackRunState === 'running'"
    >
      <ion-icon name="close" (click)="modalCtrl.dismiss()"></ion-icon>
    </div>
  </ost-layout-item>

  <ost-layout-item align="top-right">
    <!-- <ion-icon name="help-circle-outline" class="help"></ion-icon> -->
    <!-- <div class="sunny-toggle" *ngIf="showWeatherSwitch">
      <ion-label>{{ isSunny ? '☀️ 晴天' : '🌧️ 雨天' }}</ion-label>
      <ion-toggle [(ngModel)]="isSunny" (ionChange)="onToggleChange($event)"></ion-toggle>
    </div> -->
    <div class="video-monitor-text"
        [class.disabled]="modalStates.video"
        (click)="onVideoClick()">
      <div>视频</div>
      <div>监控</div>
    </div>
  </ost-layout-item>

  <ost-layout-item align="bottom-center">
    <task-control-bar
      #trackBar
      [task]="task"
      [executState]="executState"
      [mapCmpt]="ostMap"
      [currentTime]="currentTime"
      (evReportClick)="onEvReportClick($event)"
      (keyPointClick)="onKeyPointClick($event)"
      (clockInClick)="seePoints()"
    >
    </task-control-bar>
    <!-- 打卡按钮 -->
    <!-- <div *ngIf="canPunchIn" class="punch-in-fab" (click)="onPunchIn()">
      <ion-icon name="checkmark-done-outline"></ion-icon>
    </div> -->
  </ost-layout-item>
</ost-map>