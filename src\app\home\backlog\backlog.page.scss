@import "../../../theme/variables.scss";
// --ion-color-primary-contrast 白色
// --ion-color-primary 默认蓝色
.backlog {
  margin: 0;
  padding: 0;
  background-color: var(--ion-color-primary-contrast);
  width: 100%;

  // 标题区域
  .backlog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-bottom: 1px solid #f6f6f6;

    .backlog-title {
      color: var(--ion-color-primary);
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }

    .backlog-date {
      font-size: 14px;
      color: #666;
    }
  }

  // 内容区域
  .backlog-content {
    // 加载状态
    .loading-state {
      width: 100%;
      height: 160px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 0px 12px 0px;
      color: #666666;

      .loading-text {
        font-size: 12px;
        margin-left: 8px;
      }
    }

    // 任务列表
    .task-list {
      .task-container {
        height: 165px;
        overflow-y: auto;

        .task-item {
          border-bottom: 1px solid #f6f6f6;

          .task-info {
            margin: 0;
            padding: 6px 0px;

            .task-name {
              font-size: 14px;
              margin: 0 0 4px 0;
              color: #333;
            }

            .task-progress {
              font-size: 12px;
              margin: 0;
              color: #666;
            }
          }

          .task-action-btn {
            margin-inline-start: 10px;
          }
        }
      }

      // 查看更多按钮
      .view-more-btn {
        width: 100%;
        border: none;
        background: transparent;
        border-top: 1px solid #f6f6f6;
        text-align: center;
        line-height: 18px;
        padding: 6px 0px;
        font-size: 12px;
        color: #666;
        cursor: pointer;
      }
    }
  }

  // 空状态
  .empty-state {
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 0px 12px 0px;

    .empty-icon {
      height: 45px;
    }

    .empty-text {
      padding-top: 10px;
      font-size: 12px;
      color: #666666;
    }
  }
}

// 保持向后兼容的旧样式类
.titlebox {
  display: flex;
  justify-content: space-between;
  padding-top: 8px;
  padding-bottom: 8px;

  .title {
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    margin-left: 16px;
    height: 18px;
    line-height: 18px;

    img {
      width: 20px;
      height: 20px;
    }

    span {
      color: var(--ion-color-primary);
      font-size: 14px;
      height: 18px;
      line-height: 18px;
      padding-top: 2px;
    }
  }

  .date {
    font-size: 14px;
    margin-right: 16px;
  }
}

.backlogList {
  font-size: 12px;

  .listBox {
    height: 129px;
    overflow-y: auto;

    .self-note-end {
      margin-inline-start: 10px;
      padding-inline-start: 0px;
    }

    .startTask {
      background: var(--ion-color-primary);
      color: white;
      border-radius: 4px;
      padding: 2px 6px;
    }
  }

  .more {
    border-top: 1px solid #f6f6f6;
    text-align: center;
    line-height: 18px;
    padding: 6px 0px;
  }
}

.loading {
  width: 100%;
  height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 0px 12px 0px;
  color: #666666;
}