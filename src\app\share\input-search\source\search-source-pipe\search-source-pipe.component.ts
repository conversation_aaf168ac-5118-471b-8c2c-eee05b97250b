import { Component, Input, OnInit } from '@angular/core';
import { OstTreeListItem } from 'src/app/share/ost-tree-list/ost-tree-list.service';
import { InputSearchSourceService } from '../../input-search-source.service';

@Component({
  selector: 'search-source-pipe',
  templateUrl: './search-source-pipe.component.html',
  styleUrls: ['./search-source-pipe.component.scss']
})
export class SearchSourcePipeComponent implements OnInit {
  // 部门编码
  @Input() depCode: string;
  @Input() interfaceUrl: string;
  constructor(public searchSer: InputSearchSourceService) { }

  ngOnInit(): void {

  }

  onToggleSubMenu(item: OstTreeListItem): void {
    this.searchSer.change(item.data.pipelineName, item.data.pipelineCode);
  }
  
  onItemClick(item: OstTreeListItem): void {
    this.searchSer.change(item.data.pipelineName, item.data.pipelineCode);
  }
}
