
<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="ion-text-center ion-no-padding">节点信息 {{modelMode}}</ion-title>
    <ion-buttons slot="end">
      <ion-button *ngIf="modelMode===DetailsMode.ADD" (click)="onSubmit()">
        保存
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ost-form-list [formGroup]="infoFormGroup" [modelMode]="modelMode">
    <ost-form-item>
      <ion-label>事项类型:</ion-label>
      <ost-input-search 
        [readonly]="modelMode === DetailsMode.SEE"
        placeholder="请选择事项类型" slot="end"
        icon="caret-down-outline"
        [name]="modelInfo.eventType"
        formControlName="eventType">
        <ost-option-source 
          interfaceUrl="/work-inspect/api/v2/inspect/dict/msg/byDictCode?dictCode=eventType"
          labelName="dictValue"
          labelValue="dictValue"
          [radioValue]="modelInfo.eventType"
        ></ost-option-source>
      </ost-input-search>
      <ost-error errorCode="required">事项类型不能为空</ost-error>
    </ost-form-item>

    <ost-form-item>
      <ion-label>是否完成：</ion-label>
      <ion-select formControlName="status" interface="popover">
        <ion-select-option value="未完成">未完成</ion-select-option>
        <ion-select-option value="已完成">已完成</ion-select-option>
      </ion-select>
    </ost-form-item>

    <ost-form-item [required]="false" *ngIf="modelMode !== DetailsMode.ADD">
      <ion-label>上报人:</ion-label>
      <ion-input formControlName="userName" [readonly]="true"></ion-input>
      <ost-error errorCode="required">上报人不能为空</ost-error>
    </ost-form-item>

    <ost-form-item [required]="false">
      <ion-label position="stacked">描述：</ion-label>
      <ion-textarea formControlName="describe" rows="4"></ion-textarea>
    </ost-form-item>
  </ost-form-list>
  <!-- 图片 -->
  <div>
    <ost-images [modelMode]="modelMode" 
      [fileCodes]="modelInfo.fileCodes"
      (base64ImageDataChange)="handleBase64ImageDataChange($event)"
    ></ost-images>
  </div>
</ion-content>
