<!-- 关键点打卡页面 -->
<ion-content [style.height]="isViewMode ? '100%' : 'calc(100% - 56px)'">
  <!-- 关键点基础信息展示区 -->
  <div class="keypoint-info-list">
    <div class="keypoint-info-row">
      <span class="keypoint-label point">🏢 部门：</span>
      <span class="keypoint-value">{{ currentKeyPoint?.depName }}</span>
    </div>
    <div class="keypoint-info-row">
      <span class="keypoint-label point">🚩 桩号：</span>
      <span class="keypoint-value">{{ currentKeyPoint?.stakeName }}</span>
    </div>
    <div class="keypoint-info-row">
      <span class="keypoint-label point">📍 关键点：</span>
      <span class="keypoint-value">{{ currentKeyPoint?.pointName }}</span>
    </div>
    <div class="keypoint-info-row">
      <span class="keypoint-label coord">🧭 坐标：</span>
      <span class="keypoint-value">{{ formattedPoint }}</span>
    </div>
  </div>

  <ost-images
    [modelMode]="modelMode"
    [uploadMode]="isViewMode ? 'fileCode' : 'base64'"
    [fileCodes]="fileCodes"
    [base64Images]="base64Images"
    [cameraOnly]="true"
    (base64ImagesChange)="onBase64ImagesChange($event)"
  >
  </ost-images>
</ion-content>

<!-- 底部统一提交按钮 -->
<ion-footer *ngIf="!isViewMode">
  <ion-toolbar>
    <ion-button
      expand="block"
      (click)="onSubmit()"
      [disabled]="isSubmitDisabled"
      [color]="isSubmitDisabled ? 'medium' : 'primary'"
    >
      {{ isImageUploading ? '上传中...' : '提交' }}
    </ion-button>
  </ion-toolbar>
</ion-footer>