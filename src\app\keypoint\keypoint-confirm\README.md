# 关键点确认功能使用说明

## 功能概述

关键点确认功能允许用户查看未确认的关键点，并对其进行采用或删除操作。功能采用上下布局设计，上半部分为地图显示，下半部分为列表展示。

## 路由访问

```typescript
// 访问关键点确认页面
this.router.navigate(['/keypoint/confirm']);
```

## 主要功能

### 1. 数据展示
- 地图显示未确认关键点的位置
- 列表展示关键点的详细信息
- 实时统计未确认关键点数量和已选择数量

### 2. 交互操作
- 地图与列表联动选择
- 支持单个关键点的采用/删除
- 支持批量操作（批量采用/批量删除）
- 下拉刷新数据

### 3. 状态管理
- 选中状态的视觉反馈
- 操作过程中的加载状态
- 操作结果的提示信息

## 组件结构

### 主页面组件
- `KeypointConfirmPage`: 主页面组件，负责数据管理和业务逻辑

### 子组件
- `ConfirmMapViewComponent`: 地图显示组件
- `ConfirmListViewComponent`: 列表显示组件

### 服务
- `KeypointConfirmService`: 数据服务，封装API接口

## API接口

### 1. 获取未确认关键点列表
```typescript
this.keypointConfirmService.getUnconfirmedList(params).subscribe(result => {
  // 处理结果
});
```

### 2. 获取未确认关键点数量
```typescript
this.keypointConfirmService.getUnconfirmedCount(params).subscribe(result => {
  // 处理结果
});
```

### 3. 确认关键点
```typescript
this.keypointConfirmService.confirmKeyPoint({
  isOk: 'yes', // 或 'no'
  pointCode: 'POINT_CODE'
}).subscribe(result => {
  // 处理结果
});
```

## 数据模型

### UnconfirmedKeyPoint
```typescript
interface UnconfirmedKeyPoint {
  pointCode: string;           // 关键点编码
  bufferRange: number;         // 缓冲范围
  pointName: string;           // 关键点名称
  geom: string;               // 几何信息 (GeoJSON格式)
  stakeName: string;          // 桩号名称
  depName: string;            // 部门名称
  isItRaining: string;        // 是否雨天
  depCode: string;            // 部门编码
  stakeCode: string;          // 桩号编码
  inspectionMethod: string;   // 巡检方式
  selected?: boolean;         // 前端选中状态
}
```

## 样式定制

### 主要CSS类
- `.map-container`: 地图容器样式
- `.list-container`: 列表容器样式
- `.count-info`: 统计信息样式
- `.batch-actions`: 批量操作按钮样式
- `.selected`: 选中状态样式

### 响应式设计
- 支持不同屏幕尺寸的自适应布局
- 地图和列表高度比例可根据屏幕高度调整

## 注意事项

### 1. 地图组件依赖
- 需要确保 `MapComponent` 已正确导入和配置
- 地图组件的具体实现需要根据项目中使用的地图库进行调整

### 2. 权限控制
- 确保用户有访问未确认关键点的权限
- 确保用户有执行确认操作的权限

### 3. 错误处理
- 网络请求失败时会显示错误提示
- 操作失败时会显示相应的错误信息

### 4. 性能优化
- 使用 `trackBy` 函数优化列表渲染性能
- 使用 `ChangeDetectionStrategy.OnPush` 优化组件性能

## 扩展功能

### 1. 筛选功能
可以扩展添加筛选条件，如：
- 按部门筛选
- 按巡检方式筛选
- 按是否雨天筛选

### 2. 导出功能
可以添加导出未确认关键点列表的功能

### 3. 统计图表
可以添加关键点确认情况的统计图表

## 开发建议

1. **测试**: 建议为每个组件编写单元测试
2. **文档**: 保持代码注释的完整性
3. **性能**: 注意大数据量时的性能优化
4. **用户体验**: 关注操作反馈和加载状态的用户体验