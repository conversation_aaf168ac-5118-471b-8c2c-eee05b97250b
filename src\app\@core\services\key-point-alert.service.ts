import { Injectable, NgZone } from '@angular/core';
import { Vibration } from '@ionic-native/vibration/ngx';
import { TextToSpeech } from '@ionic-native/text-to-speech/ngx';
import { Platform } from '@ionic/angular';

@Injectable({ providedIn: 'root' })

/**
 * 关键点提醒服务
 */
export class KeyPointAlertService {
  private keyPointList: any[] = [];
  private reminded: boolean[] = [];
  private onAlertCallback?: (index: number, point: number[], pointName: string) => void;

  constructor(
    private vibration: Vibration,
    private tts: TextToSpeech,
    private ngZone: NgZone,
    private platform: Platform
  ) { }

  /**
   * 将数字转换为TTS友好的格式
   * 例如: "001+12945.3" -> "零零一加一二九四五点三"
   */
  private convertNumbersForTTS(text: string): string {
    if (!text) return text;
    
    // 数字到中文的映射
    const numberMap: { [key: string]: string } = {
      '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
      '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
    };
    
    // 特殊字符映射
    const specialCharMap: { [key: string]: string } = {
      '.': '点',  // 小数点转换为中文"点"
      '+': '加',  // 加号转换为中文"加"
      '-': '减',  // 减号转换为中文"减"
      '(': '左括号',
      ')': '右括号'
    };
    
    // 保持原样的字符
    const keepChars = [' ', '_'];
    
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      if (numberMap[char]) {
        // 数字转换为中文
        result += numberMap[char];
      } else if (specialCharMap[char]) {
        // 特殊字符转换为中文
        result += specialCharMap[char];
      } else if (keepChars.includes(char)) {
        // 保持原样的字符
        result += char;
      } else {
        // 其他字符（如中文、英文）保持原样
        result += char;
      }
    }
    
    return result;
  }

  /**
   * 初始化关键点列表
   */
  init(keyPointList: any[]): void {
    this.keyPointList = (keyPointList || []).map(item => ({
      ...item,
      pointArr: typeof item.point === 'string' ? JSON.parse(item.point) : item.point
    }));
    this.reminded = new Array(this.keyPointList.length).fill(false);
  }

  /**
   * 位置更新时调用
   */
  updateLocation(currentCoord: number[]): void {
    if (!this.keyPointList?.length) return;
    this.keyPointList.forEach((pointObj, idx) => {
      const distance = KeyPointAlertService.getDistanceMeters(currentCoord, pointObj.pointArr);
      if (distance <= (pointObj.bufferRange || 2)) {
        if (!this.reminded[idx]) { // 如果未提醒过
          this.triggerAlert(idx, pointObj);
          this.reminded[idx] = true;  // 设置为已提醒
        }
      } else {
        this.reminded[idx] = false; // 离开范围重置状态
      }
    });
  }

  /**
   * 重置所有关键点的提醒状态
   */
  reset(): void {
    this.reminded = new Array(this.keyPointList.length).fill(false);
  }

  /**
   * 设置提醒回调（如需在提醒时执行额外操作）
   */
  setOnAlert(callback: (index: number, point: number[], pointName: string) => void): void {
    this.onAlertCallback = callback;
  }

  /**
   * 触发提醒
   */
  triggerAlert(index: number, pointObj: any): void {
    // 使用 NgZone.runOutsideAngular 确保在后台状态下也能正常执行
    this.ngZone.runOutsideAngular(() => {
      // 确保在真机环境下执行
      if (this.platform.is('cordova')) {
        try {
          // 震动提醒
          this.vibration.vibrate([500, 100, 500, 100, 500]);
          console.log('🔔 关键点震动提醒已触发:', pointObj.pointName);
        } catch (e) {
          console.error('震动提醒失败:', e);
        }

        try {
          // TTS 语音播报
          const name = pointObj.pointName || '关键点';
          const ttsName = this.convertNumbersForTTS(name);
          this.tts.speak({
            text: `您已到达${ttsName}关键点`,
            locale: 'zh-CN',
            rate: 0.9
          }).then(() => {
            console.log('🔊 TTS 播报成功:', ttsName);
          }).catch(e => {
            console.error('TTS 播报失败:', e);
          });
        } catch (e) {
          console.error('TTS 初始化失败:', e);
        }
      } else {
        // 开发环境下的模拟提醒
        console.log('🔔 [开发环境] 关键点提醒:', pointObj.pointName);
      }

      // 执行回调（在 Angular Zone 内执行）
      if (this.onAlertCallback) {
        this.ngZone.run(() => {
          this.onAlertCallback(index, pointObj.pointArr, pointObj.pointName);
        });
      }
    });
  }

  /**
   * Haversine 公式计算两点间距离（米）
   */
  static getDistanceMeters([lng1, lat1]: number[], [lng2, lat2]: number[]): number {
    const toRad = (d: number) => d * Math.PI / 180;
    const R = 6371000;
    const dLat = toRad(lat2 - lat1);
    const dLng = toRad(lng2 - lng1);
    const a = Math.sin(dLat / 2) ** 2 +
      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
      Math.sin(dLng / 2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * 判断当前位置是否在任一关键点的指定范围内，并返回命中的关键点
   */
  isInRange(currentCoord: number[]): { inRange: boolean, keyPoint?: any } {
    if (!this.keyPointList?.length) return { inRange: false };
    for (const pointObj of this.keyPointList) {
      if (KeyPointAlertService.getDistanceMeters(currentCoord, pointObj.pointArr) <= (pointObj.bufferRange || 2)) {
        return { inRange: true, keyPoint: pointObj };
      }
    }
    return { inRange: false };
  }
} 