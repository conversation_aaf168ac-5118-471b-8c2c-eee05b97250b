import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { StorageService } from '../../../@core/providers/storage.service';

export interface LocationProviderOption {
  value: number;
  label: string;
  description: string;
  icon: string;
}

@Injectable({
  providedIn: 'root'
})
export class LocationProviderService {
  private readonly STORAGE_KEY = 'LocationProviderMode';
  private readonly DEFAULT_PROVIDER = 3; // 默认混合定位

  // 定位模式选项
  public readonly locationProviderOptions: LocationProviderOption[] = [
    {
      value: 1,
      label: 'GPS定位',
      description: '仅使用GPS，精度最高但室内信号差',
      icon: 'navigate-outline'
    },
    {
      value: 2,
      label: '网络定位',
      description: '使用网络定位，速度快但精度较低',
      icon: 'wifi-outline'
    },
    {
      value: 3,
      label: '混合定位',
      description: 'GPS+网络混合，平衡精度和速度（推荐）',
      icon: 'location-outline'
    }
  ];

  private currentProviderSubject = new BehaviorSubject<number>(this.DEFAULT_PROVIDER);
  public currentProvider$ = this.currentProviderSubject.asObservable();

  constructor(private storage: StorageService) {
    this.loadSavedProvider();
  }

  /**
   * 加载保存的定位模式
   */
  private async loadSavedProvider(): Promise<void> {
    try {
      const savedProvider = await this.storage.get(this.STORAGE_KEY).toPromise();
      if (savedProvider && this.isValidProvider(savedProvider)) {
        this.currentProviderSubject.next(savedProvider);
      } else {
        // 如果没有保存的配置或配置无效，使用默认的混合定位模式并保存
        console.log('使用默认混合定位模式:', this.DEFAULT_PROVIDER);
        this.currentProviderSubject.next(this.DEFAULT_PROVIDER);
        // 保存默认配置到存储中
        await this.storage.set(this.STORAGE_KEY, this.DEFAULT_PROVIDER).toPromise();
      }
    } catch (error) {
      console.warn('加载定位模式失败，使用默认混合定位模式', error);
      // 发生错误时也确保使用默认的混合定位模式
      this.currentProviderSubject.next(this.DEFAULT_PROVIDER);
    }
  }

  /**
   * 验证定位模式是否有效
   */
  private isValidProvider(provider: number): boolean {
    return this.locationProviderOptions.some(option => option.value === provider);
  }

  /**
   * 获取当前定位模式
   */
  getCurrentProvider(): number {
    return this.currentProviderSubject.value;
  }

  /**
   * 设置定位模式
   */
  async setProvider(provider: number): Promise<void> {
    if (!this.isValidProvider(provider)) {
      throw new Error(`无效的定位模式: ${provider}`);
    }

    try {
      // 1. 保存定位模式选择
      await this.storage.set(this.STORAGE_KEY, provider).toPromise();

      // 2. 更新GeolocationConfig中的locationProvider
      await this.updateGeolocationConfig(provider);

      // 3. 通知订阅者
      this.currentProviderSubject.next(provider);
      console.log(`定位模式已切换为: ${this.getProviderLabel(provider)}`);
    } catch (error) {
      console.error('保存定位模式失败', error);
      throw error;
    }
  }

  /**
   * 更新GeolocationConfig中的locationProvider
   */
  private async updateGeolocationConfig(newProvider: number): Promise<void> {
    try {
      const existingConfig = await this.storage.get('GeolocationConfig').toPromise();

      if (existingConfig) {
        // 更新现有配置中的locationProvider
        const updatedConfig = { ...existingConfig, locationProvider: newProvider };
        await this.storage.set('GeolocationConfig', updatedConfig).toPromise();
        console.log(`GeolocationConfig已更新，locationProvider: ${existingConfig.locationProvider} -> ${newProvider}`);
      } else {
        console.warn('未找到GeolocationConfig，无法更新locationProvider');
      }
    } catch (error) {
      console.error('更新GeolocationConfig失败', error);
      // 不抛出错误，避免影响定位模式切换的主要流程
    }
  }

  /**
   * 获取定位模式标签
   */
  getProviderLabel(provider: number): string {
    const option = this.locationProviderOptions.find(opt => opt.value === provider);
    return option ? option.label : '未知模式';
  }

  /**
   * 获取定位模式描述
   */
  getProviderDescription(provider: number): string {
    const option = this.locationProviderOptions.find(opt => opt.value === provider);
    return option ? option.description : '';
  }

  /**
   * 获取定位模式图标
   */
  getProviderIcon(provider: number): string {
    const option = this.locationProviderOptions.find(opt => opt.value === provider);
    return option ? option.icon : 'location-outline';
  }

  /**
   * 监听定位模式变化
   */
  onProviderChange(): Observable<number> {
    return this.currentProvider$;
  }

  /**
   * 获取当前GeolocationConfig中的locationProvider（用于调试）
   */
  async getCurrentGeolocationConfigProvider(): Promise<number | null> {
    try {
      const config = await this.storage.get('GeolocationConfig').toPromise();
      return config ? config.locationProvider : null;
    } catch (error) {
      console.error('获取GeolocationConfig失败', error);
      return null;
    }
  }
}
