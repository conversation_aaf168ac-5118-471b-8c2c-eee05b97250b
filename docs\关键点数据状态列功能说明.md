# 关键点数据状态列功能说明

## 功能概述

在任务详情的关键点列表中添加了一个"数据"列，用于显示关键点的打卡数据或未巡检原因状态。该功能允许用户快速识别哪些关键点有相关数据，并可以点击查看详细信息。

## 功能特性

### 1. 数据状态显示
- **打卡数据**：当关键点的 `picCode` 字段有值时，显示绿色相机图标
- **未巡检原因**：当关键点的 `reason` 字段有值时，显示橙色警告图标
- **无数据**：当两个字段都没有值时，显示 "-" 符号

### 2. 条件显示
- **任务详情页面**：显示数据状态列（`showDataStatus=true`）
- **执行页面**：不显示数据状态列（`showDataStatus=false`）

### 3. 交互功能
- 点击相机图标：打开打卡模态框组件并跳转到"关键点打卡"标签页，以查看模式显示已有的打卡数据
- 点击警告图标：打开打卡模态框组件并跳转到"未巡检原因"标签页，以查看模式显示未巡检原因
- 点击事件不会触发行点击事件（使用事件阻止冒泡机制）
- 直接使用图标，无按钮包装，提供更简洁的界面
- 查看模式下自动回显数据，隐藏提交按钮，提供只读查看体验

## 技术实现

### 1. 组件属性
```typescript
// 是否显示数据状态列
@Input() showDataStatus = true;
```

### 2. 数据模型扩展
```typescript
interface KeyPoint {
  pointName: string;
  isItRaining: string;
  state: string;
  picCode?: string | string[];  // 打卡照片编码
  reason?: string;              // 未巡检原因
}
```

### 3. 核心方法
- `hasPicCode(keyPoint)`: 检查是否有打卡数据
- `hasReason(keyPoint)`: 检查是否有未巡检原因
- `onDataStatusClick(keyPoint, event, tabType)`: 处理数据状态点击事件，以查看模式打开打卡模态框组件并跳转到指定标签页

### 4. 查看模式功能
- **打卡模态框组件**: 支持 `modelMode` 和 `isDetailMode` 属性控制查看模式
- **打卡组件**: 在查看模式下自动回显 `picCode` 数据，隐藏提交按钮
- **未巡检原因组件**: 在查看模式下自动回显 `reason` 数据，设置只读状态
- **数据回显**: 自动解析并显示已有的打卡照片和未巡检原因文本

## 使用方式

### 任务详情页面
```html
<app-key-points 
  [taskCode]="modelInfo.taskCode" 
  [showClose]="false" 
  [showDataStatus]="true">
</app-key-points>
```

### 执行页面
```typescript
await this.modalManagerService.createModalWithGuard('points', {
  component: KeyPointsComponent,
  componentProps: {
    disabledClick: false,
    taskCode: this.task.taskCode,
    loading$: this.loading$,
    keyPoints$: this.keyPoints$,
    showDataStatus: false, // 不显示数据状态列
  },
  cssClass: 'camera-list-modal'
});
```

## 样式说明

### 样式类
- `.data-status-col`: 数据状态列样式
- `.data-icon`: 数据图标样式
- `.clickable`: 可点击图标的交互效果
- `.no-data`: 无数据时的文本样式

### 图标颜色
- 打卡数据：`color="success"` (绿色)
- 未巡检原因：`color="warning"` (橙色)

### 图标交互效果
- 悬停时：图标放大1.1倍
- 点击时：图标缩小0.95倍
- 过渡动画：0.2秒缓动效果

## 扩展功能

### 待实现功能
1. **数据统计**：在列表头部显示有数据的关键点统计信息
2. **筛选功能**：根据数据状态筛选关键点列表
3. **批量操作**：支持批量查看或处理关键点数据

### 可能的改进
1. 支持更多数据类型的显示
2. 添加数据时间戳显示
3. 支持批量操作功能

## 注意事项

1. 该功能依赖于后端接口返回的数据结构中包含 `picCode` 和 `reason` 字段
2. 在执行页面中默认不显示该列，以保持界面简洁
3. 点击数据状态图标不会触发行的点击事件，避免意外操作
4. 当关键点既有打卡数据又有未巡检原因时，优先显示打卡数据图标
5. 打卡模态框组件需要支持 `initialTab`、`modelMode` 和 `isDetailMode` 属性
6. 图标直接使用，无按钮包装，提供更简洁的用户体验
7. 查看模式下所有输入控件都设置为只读状态，提交按钮被隐藏
8. 数据回显支持字符串和数组格式的 `picCode` 字段
