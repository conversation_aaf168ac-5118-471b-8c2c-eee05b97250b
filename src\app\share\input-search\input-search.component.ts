import { Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { <PERSON>dal<PERSON>ontroller } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { DetailsMode } from 'src/app/@core/base/environment';
import { InputSearchSourceComponent } from './input-search-source.component';
import { InputSearchSourceService } from './input-search-source.service';

@Component({
  selector: 'ost-input-search',
  templateUrl: './input-search.component.html',
  styleUrls: ['./input-search.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputSearchComponent),
      multi: true
    },
    InputSearchSourceService],

})
export class InputSearchComponent implements OnInit, ControlValueAccessor, On<PERSON><PERSON>roy {
  DetailsMode: typeof DetailsMode = DetailsMode;
  @ViewChild('sourceContainer', { static: false }) searchTemplate: TemplateRef<any>;
  // 预设值
  @Input() placeholder = '';
  // 显示值
  @Input() name;
  // 是否只读
  @Input() readonly = false;
  // 级联
  @Input() cascade: InputSearchComponent;
  // 图标
  @Input() icon = 'search-outline';
  @Output() valueChange: EventEmitter<any> = new EventEmitter();
  valueChange$: Subscription;
  private onChange = (_: any) => null;
  private onTouch = () => null;

  constructor(
    private modalCtrl: ModalController,
    public searchSer: InputSearchSourceService,
  ) { }
  ngOnDestroy(): void {
    this.valueChange$.unsubscribe();
  }

  ngOnInit(): void {
    this.valueChange$ = this.searchSer.onValueChange().subscribe((ret: { name: string, value: any }) => {
      this.name = ret.name;
      this.valueChange.emit(ret);
      this.onChange(ret.value);
      if (this.cascade) {
        this.cascade.clear();
        this.cascade.onSourceOpen();
      }
    });
  }

  /**
   * 清除
   */
  clear(): void {
    this.onChange(null);
  }

  async onSourceOpen(): Promise<void> {
    if (!this.readonly) {
      this.onTouch();
      // 创建模态窗口
      const modal = await this.modalCtrl.create({
        component: InputSearchSourceComponent,
        componentProps: {
          tplRef: this.searchTemplate
        }
      });
      await modal.present();
    }
  }


  writeValue(obj: any): void {
    if (obj === null) {
      this.name = obj;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

}
