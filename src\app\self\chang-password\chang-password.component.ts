import { Component, HostL<PERSON>ener, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ModalController, NavController, ToastController } from '@ionic/angular';
import { RequestResult } from 'src/app/@core/base/request-result';
import { OSTValidators } from 'src/app/@core/base/validators';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { SelfService } from '../self.service';
import { JSEncrypt } from 'jsencrypt/lib/JSEncrypt';
import { RSAPUBLICKEY } from 'src/app/auth/class/auth';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { StorageService } from 'src/app/@core/providers/storage.service';

@Component({
  selector: 'app-chang-password',
  templateUrl: './chang-password.component.html',
  styleUrls: ['./chang-password.component.scss']
})
export class ChangPasswordComponent implements OnInit, OnDestroy {
  // 表单
  infoFormGroup: FormGroup;
  // 密码修改信息
  ChangPasswordInfo: ChangPasswordInfo = new ChangPasswordInfo();
  // 公钥
  publicKey = RSAPUBLICKEY;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  pwShow = 'password';
  isShow = 'eye-off';
  showClose = true;
  account = '';
  constructor(
    public modalController: ModalController, private fb: FormBuilder, public nav: NavController,
    public netSer: SelfService, public toastController: ToastController, private ngZone: NgZone,
    public userSer: UserInfoService, private route: ActivatedRoute,
    private storage: StorageService,
  ) { }

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.showClose = params['isModal'];
        this.account = this.getAccountFromParamsOrService(params);
      });
    this.initForm();

    // 新增：监听新密码变化，刷新确认密码校验
    this.infoFormGroup.get('password').valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.infoFormGroup.get('confirmpassword').updateValueAndValidity();
      });

    // 新增：监听旧密码变化，刷新新密码校验
    this.infoFormGroup.get('oldPassword').valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.infoFormGroup.get('password').updateValueAndValidity();
      });
  }

  initForm(): void {
    // 初始化表单
    this.infoFormGroup = this.fb.group({
      account: [this.account, [Validators.required]],
      oldPassword: [this.ChangPasswordInfo.oldPassword, [Validators.required]],
      password: [
        this.ChangPasswordInfo.password,
        [
          Validators.required,
          OSTValidators.passwordValid,
          OSTValidators.notSameAsOldPassword('oldPassword')
        ]
      ],
      confirmpassword: [
        this.ChangPasswordInfo.confirmpassword,
        [Validators.required, OSTValidators.match('password')]
      ],
    });
  }

  /**
   * 密码可见
   */
  onPasswordShow(): void {
    this.pwShow === 'password' ? this.isShow = 'eye-outline' : this.isShow = 'eye-off';
    this.pwShow === 'password' ? this.pwShow = 'text' : this.pwShow = 'password';
  }

  /**
   * 修改密码
   */
  onChangPassword(): void {
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(this.publicKey);
    const oldPassword: any = encrypt.encrypt(this.infoFormGroup.get('oldPassword').value);
    const passWord: any = encrypt.encrypt(this.infoFormGroup.get('password').value);
    this.netSer.ResetPassword({
      account: this.infoFormGroup.get('account').value,
      oldUserPasd: oldPassword,
      newUserPasd: passWord
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.success, this.errors);
  }

  success = (result: RequestResult<any>) => {
    // 请求成功
    if (result.code === 0) {
      this.onToast('密码修改成功，请重新登录', 'success');
      this.goBack();
      this.userSer.logout();
      this.storage.remove('password').subscribe();
      this.ngZone.run(() => {
        this.nav.navigateRoot('/auth');
      });
    } else {
      this.errors(result);
    }
  }

  errors = (err) => {
    this.onToast(err.body.msg, 'danger');
  }

  /**
   * 提醒
   */
  async onToast(mgs = '出现错误请重试！', color = 'primary'): Promise<void> {
    const toast = await this.toastController.create({
      message: mgs,
      duration: 2000,
      color
    });
    toast.present();
  }

  /**
   * 返回
   */
  goBack(): void {
    this.modalController.dismiss();
  }
  // 侧滑返回
  @HostListener('document:ionBackButton', ['$event'])
  backButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalController.dismiss();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

  // 新增私有方法，封装account优先级逻辑
  private getAccountFromParamsOrService(params: any): string {
    return params['account'] ?? this.userSer.account ?? '';
  }
}

export class ChangPasswordInfo {
  // 旧密码
  oldPassword = '';
  // 密码
  password = '';
  // 密码确认
  confirmpassword = '';
}
