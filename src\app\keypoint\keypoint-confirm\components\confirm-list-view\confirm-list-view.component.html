<div class="confirm-list-container">
  <!-- 空状态 -->
  <div *ngIf="points.length === 0" class="empty-state">
    <ion-icon name="checkmark-done-circle" color="success"></ion-icon>
    <p>暂无未确认的关键点</p>
  </div>

  <!-- 关键点列表 -->
  <ion-list *ngIf="points.length > 0" class="points-list">
    <ion-item 
      *ngFor="let point of points; trackBy: trackByPointCode"
      (click)="onItemClick(point, $event)">
      

      
      <!-- 关键点信息 -->
      <ion-label class="point-info">
        <h2 class="point-name">{{point.pointName}}</h2>
        
        <div class="point-details">
          <p class="detail-item">
            <ion-icon name="business" color="medium"></ion-icon>
            <span>{{point.depName}}</span>
          </p>
          
          <p class="detail-item">
            <ion-icon name="location" color="medium"></ion-icon>
            <span>桩号: {{point.stakeName}}</span>
          </p>
          
          <p class="detail-item">
            <ion-icon name="eye" color="medium"></ion-icon>
            <span>{{point.inspectionMethod}}</span>
            <span *ngIf="point.isItRaining === '是'" class="rain-badge">
              <ion-icon name="rainy" color="warning"></ion-icon>
              雨天不巡
            </span>
          </p>
          
          <p class="detail-item" *ngIf="point.bufferRange">
            <ion-icon name="radio-button-on" color="medium"></ion-icon>
            <span>缓冲范围: {{point.bufferRange}}m</span>
          </p>
        </div>
      </ion-label>
      
      <!-- 操作按钮 -->
      <div slot="end" class="action-buttons">
        <ion-button 
          fill="solid" 
          color="success"
          size="small"
          class="action-btn approve-btn"
          (click)="onConfirmSingle(point, true, $event)">
          <ion-icon name="checkmark" slot="start"></ion-icon>
          采用
        </ion-button>
        
        <ion-button 
          fill="solid" 
          color="danger"
          size="small"
          class="action-btn reject-btn"
          (click)="onConfirmSingle(point, false, $event)">
          <ion-icon name="close" slot="start"></ion-icon>
          废弃
        </ion-button>
      </div>
    </ion-item>
  </ion-list>
</div>