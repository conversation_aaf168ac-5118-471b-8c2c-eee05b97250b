@import "../../../../theme/variables.scss";
.check-update-header {
  text-align: center;
  height: 20%;
  line-height: 35px;
  width: 100%;
  color: var(--ion-color-primary-contrast);
  background-color: var(--ion-color-primary);
}
.check-update-context {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 60%;
  white-space: pre-line;
  overflow-y: auto;
  padding: 4px 0px 4px 12px;
}
.check-update-footer {
  border-top: 1px solid #f6f6f6;
  height: 20%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.btn-confirm {
  width: 49%;
  text-align: center;
  color: var(--ion-color-primary);
}
.btn-cancal {
  width: 49%;
  text-align: center;
  color: #666;
}
