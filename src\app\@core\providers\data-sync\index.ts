/**
 * 数据同步服务模块导出
 * 提供统一的导入入口
 */

// 主服务
export { DataSyncManagerService } from './data-sync-manager.service';

// 子服务
export { SyncCacheManagerService } from './sync-cache-manager.service';
export { SyncUploadManagerService } from './sync-upload-manager.service';
export { SyncRetryManagerService } from './sync-retry-manager.service';

// 类型定义
export * from './types/sync-data.types';

/**
 * 使用示例：
 * 
 * // 基本使用（推荐）
 * import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';
 * 
 * // 高级使用（直接使用子服务）
 * import { 
 *   SyncCacheManagerService, 
 *   SyncUploadManagerService, 
 *   SyncRetryManagerService 
 * } from 'src/app/@core/providers/data-sync';
 * 
 * // 类型导入
 * import { SyncCacheItem, FailedDataGroup } from 'src/app/@core/providers/data-sync';
 */
