<div class="camera-container" [class.fullscreen]="isFullScreen">
  <!-- 右上角控制按钮 -->
  <div class="controls-top-right">
    <ion-button fill="clear" color="light" (click)="toggleFullScreen()">
      <ion-icon [name]="isFullScreen ? 'contract-outline' : 'expand-outline'"></ion-icon>
    </ion-button>
    <ion-button fill="clear" color="light" (click)="closeModal()">
      <ion-icon name="close-outline"></ion-icon>
    </ion-button>
  </div>
  <!-- 视频播放区域 -->
  <div class="video-section">
    <video #videoPlayer class="video-player" autoplay>
      您的浏览器不支持视频播放
    </video>
    <canvas #overlayCanvas class="overlay-canvas"></canvas>

    <div *ngIf="showLoading" class="loading-spinner">
      <ion-spinner name="circles"></ion-spinner>
      <p>加载中...</p>
    </div>
    
    <!-- 错误提示区域 -->
    <div class="error-container" *ngIf="showError">
      <div class="error-message">
        <ion-icon name="warning-outline" color="danger"></ion-icon>
        <span>{{ errorMessage }}</span>
      </div>
      <div class="error-actions">
        <!-- 重试按钮 -->
        <ion-button 
          fill="clear" 
          color="light" 
          (click)="retryInitialization()"
          class="retry-button">
          <ion-icon name="refresh-outline"></ion-icon>
          重新加载
        </ion-button>
      </div>
    </div>
  </div>
</div>
