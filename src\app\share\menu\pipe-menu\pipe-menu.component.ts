import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { map } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { OstTreeListComponent } from '../../ost-tree-list/ost-tree-list.component';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';
import { ShareModuleService } from '../../share.service';
import { InputSearchSourceService } from '../../input-search/input-search-source.service';

@Component({
  selector: 'ost-pipe-menu',
  templateUrl: './pipe-menu.component.html',
  styleUrls: ['./pipe-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PipeMenuComponent implements OnInit, OnChanges {
  @ViewChild('menu', { static: false }) menu: OstTreeListComponent;
  @Input() interfaceUrl: string;
  @Input() depCode: string;

  @Output() toggleSubMenu = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<any>();

  loading: boolean;
  items: OstTreeListItem[] = [];
  constructor(
    public cd: ChangeDetectorRef, 
    public userSer: UserInfoService, 
    public netSer: ShareModuleService,
    private searchSer: InputSearchSourceService
  ) { }

  ngOnInit(): void { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.depCode && !changes.depCode.firstChange) {
      // 使用setTimeout将清空操作推迟到下一个事件循环
      setTimeout(() => {
        this.searchSer.change('', '');
      });
      
      if (this.depCode) {
        this.loadMenuTree(this.depCode);
      }
    }
  }

  /**
   * 设置选中的菜单项
   */
  setSelectItemById(id: string): void {
    // 根据id查找对应的菜单项
    const selectItem = this.items.find(itme => (itme.data.pipelineCode === id));
    if (selectItem) {
      selectItem.expanded = true;
      this.menu.setSelectItem(selectItem);
      this.cd.markForCheck();
    }
  }

  /**
   * 设置选中的菜单项
   */
  setSelectItem(item: OstTreeListItem): void {
    this.menu.setSelectItem(item);
  }

  /**
   * 切换子菜单
   */
  onToggleSubMenu(item: OstTreeListItem): void {
    this.toggleSubMenu.emit(item);
  }

  /**
   * 点击菜单项
   */
  onItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }

  /**
   * 获取管线数据
   */
  loadMenuTree(depCode: string): void {
    this.depCode = depCode;
    this.loading = true;
    this.netSer.getRequest({ interfaceUrl: this.interfaceUrl, depCode })
      .pipe(map((data: RequestResult<any[]>) => this.transformation(data)))
      .subscribe(res => {
        this.items = res;
        this.loading = false;
        this.cd.markForCheck();
      }, error => {
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  /**
   * 数据转换
   */
  transformation(res: any): OstTreeListItem[] {
    const data = res.data || res;
    return this.transformPipelineData(data);
  }

  /**
   * 将管道数据转换为树形结构
   */
  private transformPipelineData(data: any[]): OstTreeListItem[] {
    if (!data || !data.length) {
      return [];
    }

    const convertToTreeItem = (item: any): OstTreeListItem => {
      const treeItem: OstTreeListItem = {
        title: item.pipelineName,
        data: item
      };

      if (item.children && item.children.length) {
        treeItem.children = item.children.map(child => convertToTreeItem(child));
      }

      return treeItem;
    };

    return data.map(item => convertToTreeItem(item));
  }
}
