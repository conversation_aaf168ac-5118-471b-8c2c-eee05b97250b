import { AfterContentInit, ChangeDetectorRef, Component, ContentChild, ElementRef, Input, OnChanges, OnInit, Renderer2, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IonTextarea } from '@ionic/angular';
import { DetailsMode } from 'src/app/@core/base/environment';
import { OstFormItemService, OstFormService } from '../ost-form.service';

@Component({
  selector: 'ost-form-item',
  templateUrl: './form-item.component.html',
  styleUrls: ['./form-item.component.scss'],
  providers: [OstFormItemService]
})
export class FormItemComponent implements OnInit, OnChanges, AfterContentInit {
  @ViewChild('ostFormItem', { static: false }) ostFormItem: ElementRef<HTMLElement>;
  @ContentChild(IonTextarea) ionTextarea: IonTextarea;
  @Input() formGroup: FormGroup;
  // 模块形式
  @Input() modelMode: DetailsMode;
  // 表单控制器名称
  @Input() ostFormControlNames: string;
  // 是否必填
  @Input() required = true;
  // 是否可编辑
  @Input() editable: boolean;
  // 分隔线
  @Input() divLine: boolean;
  isShowTextarea = false;
  DetailsMode: typeof DetailsMode = DetailsMode;
  constructor(
    public renderer2: Renderer2, public ostFormServe: OstFormService,
    private ostFormItemServe: OstFormItemService,private cdr:ChangeDetectorRef
  ) { }

  ngOnInit(): void {

    this.ostFormItemServe.ostFormControlNames = this.ostFormControlNames;
    this.ostFormItemServe.formGroup = this.ostFormServe.formGroup;

    this.formGroup = this.formGroup || this.ostFormServe.formGroup;
    this.modelMode = this.modelMode || this.ostFormServe.modelMode;

  }

  ngAfterContentInit() {
    // 查询是否有 <ion-textarea> 作为内容被传递
    if (this.ionTextarea) {
      this.isShowTextarea = true;
      this.cdr.detectChanges();
    }
  }

  ngOnChanges(): void {
    // 子项传递
    this.ostFormItemServe.ostFormControlNames = this.ostFormControlNames;
    this.ostFormItemServe.formGroup = this.formGroup;
    this.ostFormItemServe.required = this.required;
    this.ostFormItemServe.modelMode = this.modelMode;
    // 如果已经加载子项
    if (this.ostFormItem) {
      this.changeModelMode();
    }
  }

  ngAfterViewInit(): void {
    this.changeModelMode();
  }

  changeModelMode(): void {
    const formItem = this.ostFormItem.nativeElement.children[0];
    this.changeModelInput(formItem);
    this.changeModelSelect(formItem);
    this.changeModelDatetime(formItem);
    this.changeModelTextarea(formItem);
    this.changeModelSearch(formItem);
    this.changeModelToggle(formItem);
  }

  /**
   * 修改输入框模式
   */
  changeModelInput(formItem): void {
    const inputs = formItem.getElementsByTagName('ion-input');
    const input = inputs.length > 0 ? inputs[0] : null;
    if (input) {
      const el = input as HTMLElement;
      this.ostFormItemServe.ostFormControlNames = el.getAttribute('formControlName');
      if (this.modelMode === DetailsMode.SEE) {
        this.renderer2.setAttribute(input, 'readonly', 'true');
        this.renderer2.setAttribute(input, 'class', 'ion-text-end');
      }

    }
  }

  /**
   * 修改搜索模式
   */
  changeModelSearch(formItem): void {
    // const searchs = formItem.getElementsByTagName('ost-input-search');
    // const search = searchs.length > 0 ? searchs[0] : null;
    // if (search) {
    //   const el = search as HTMLElement;
    //   this.ostFormItemServe.ostFormControlNames = el.getAttribute('formCtrlName');
    //   if (this.modelMode === DetailsMode.SEE) {
    //     const div = el.getElementsByTagName('div')[0];
    //     div.removeChild(div.getElementsByTagName('ion-button')[0]);
    //   }

    // }
  }

  /**
   * 修改文本域模式
   */
  changeModelTextarea(formItem): void {
    const textareas = formItem.getElementsByTagName('ion-textarea');
    const textarea = textareas.length > 0 ? textareas[0] : null;
    if (textarea) {
      const el = textarea as HTMLElement;
      this.ostFormItemServe.ostFormControlNames = el.getAttribute('formControlName');
      if (this.modelMode === DetailsMode.SEE) {
        this.renderer2.setAttribute(textarea, 'readonly', 'true');
      }

    }
  }

  /**
   * 修改开关模式
   */
  changeModelToggle(formItem): void {
    const toggles = formItem.getElementsByTagName('ion-toggle');
    const toggle = toggles.length > 0 ? toggles[0] : null;
    if (toggle) {
      const el = toggle as HTMLElement;
      this.ostFormItemServe.ostFormControlNames = el.getAttribute('formControlName');
      if (this.modelMode === DetailsMode.SEE) {
        this.renderer2.setAttribute(toggle, 'disabled', 'true');
      }
    }
  }

  /**
   * 修改选择器模式
   */
  changeModelSelect(formItem): void {
    const selects = formItem.getElementsByTagName('ion-select');
    const select = selects.length > 0 ? selects[0] : null;
    if (select) {
      const el = select as HTMLElement;
      this.ostFormItemServe.ostFormControlNames = el.getAttribute('formControlName');
      if (this.modelMode === DetailsMode.SEE) {
        this.renderer2.setAttribute(select, 'disabled', 'true');
      }

    }
  }

  /**
   * 修改时间选择器模式
   */
  changeModelDatetime(formItem): void {
    const selects = formItem.getElementsByTagName('ion-datetime');
    const select = selects.length > 0 ? selects[0] : null;
    if (select) {
      const el = select as HTMLElement;
      this.ostFormItemServe.ostFormControlNames = el.getAttribute('formControlName');
      if (this.modelMode === DetailsMode.SEE) {
        this.renderer2.setAttribute(select, 'disabled', 'true');
      }
    }
  }

}
