
<div class="check-update-header">
  发现新的版本
</div>
<!-- 内容 -->
<div class="check-update-context">
  <ng-container *ngIf="hasPermission else onHasPermissionMsg">
    {{versionInfo.remark}}
  </ng-container>
</div>
<!-- 按钮 -->
<div class="check-update-footer">
  <ng-container *ngIf="hasPermission else onHasPermissionBtn">
    <div
      class="btn-confirm"
      (click)="onUpdataApp(versionInfo.versionUrl)"
    >
      马上更新
    </div>
  </ng-container>
</div>

<ng-template #onHasPermissionBtn>
  <div
    class="btn-confirm"
    (click)="onToAllow()"
  >
    去允许
  </div>
</ng-template>
<ng-template #onHasPermissionMsg>
  请允许安装应用否则无法更新app!
</ng-template>