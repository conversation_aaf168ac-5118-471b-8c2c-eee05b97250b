import { DatePipe } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BackgroundGeolocation, BackgroundGeolocationConfig, BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { ModalController, ToastController } from '@ionic/angular';
import centroid from '@turf/centroid';
import { merge, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PageGridResult } from 'src/app/@core/base/request-result';
import { ExecutComponent } from 'src/app/execut/execut.component';
import { ExecutService } from 'src/app/execut/execut.service';
import { Task } from 'src/app/work/class/work';
import { PageEventService } from '../home.event';
import { HomeModuleService } from '../home.service';

@Component({
  selector: 'app-backlog',
  templateUrl: './backlog.page.html',
  styleUrls: ['./backlog.page.scss'],
})
export class BacklogPage implements OnInit, OnDestroy {
  // 检测到存在未上传数据
  onUploadLength = 0;
  // 检测到未上传原始数据
  locationConfig: BackgroundGeolocationConfig;
  // 当前日期
  nowDate = this.dateTransform(new Date(), 'yyyy-MM-dd');
  // 列表数据
  listDtata: any = [];
  // 加载状态
  isLoading = false;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private datePipe: DatePipe, private dataEvent: PageEventService,
    private netSer: HomeModuleService, private exeSer: ExecutService,
    private router: Router, private modalCtrl: ModalController,
    private backgroundGeolocation: BackgroundGeolocation,
    private toastCtrl: ToastController,
  ) { }
  ngOnInit(): void {
    // 页面数据更新
    merge(
      this.dataEvent.receiveDataForPageType('home'),
      this.dataEvent.receiveDataForPageType('refreshCompletionRate')
    )
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: boolean) => {
        if (state) {
          this.loadBacklog();
        }
      });
    this.loadBacklog();
    // 检测未上传数据
    this.checkData();
  }

  /**
   * 今日待办列表
   */
  loadBacklog(): void {
    this.isLoading = true;
    this.netSer.backlog()
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.loadBacklogSuccess, this.loadBacklogError);
  }

  loadBacklogSuccess = (result: PageGridResult<Task[]>) => {
    this.listDtata = result.data;
    this.isLoading = false;
  }

  loadBacklogError = () => {
    // this.onToast('巡检任务加载失败,请重试！');
    this.isLoading = false;
  }

  /**
   * 跳转工作页面
   */
  goWork(): void {
    this.router.navigateByUrl('tabs/work');
  }

  onStartClick(task: Task): void {
    task.status === '执行中' ? this.getTaskInfo(task) : this.startTask(task);
  }

  /**
   * 获取任务信息
   * @param task 任务对象
   */
  getTaskInfo(task: Task) {
    // 保存原始 taskCode，避免对象引用问题
    const originalTaskCode = task.taskCode;
    this.exeSer.continueTasck({ taskCode: originalTaskCode }).subscribe((ret) => {
      if (ret.code === 0 && ret.data) {
        // 保留原始的 taskCode，不让 API 返回的数据覆盖
        const { taskCode: apiTaskCode, ...apiData } = ret.data;
        const newTaskInfo = {
          ...task,
          ...apiData,
          taskCode: originalTaskCode // 强制使用原始的 taskCode
        };
        this.startTask(newTaskInfo);
      } else {
        // 如果 API 返回的数据为空，直接使用原始任务信息
        console.warn('getTaskInfo: API 返回数据为空，使用原始任务信息', ret);
        this.startTask(task);
      }
    });
  }

  /**
   * 开始任务
   */
  async startTask(task: Task): Promise<void> {
    // 1. 解析geom数据
    let geomData = JSON.parse(task.geom);
    // 2. 清理无效坐标
    geomData = this.cleanGeomCoordinates(geomData);
    // 3. 计算中心点
    const centerPoint = centroid(geomData);

    const modal = await this.modalCtrl.create({
      component: ExecutComponent,
      componentProps: {
        isModal: true, mapClick: true, task,
        centerPoint: centerPoint.geometry.coordinates,
        executState: task.status === '执行中' ? 'continue' : 'create'
      },
      backdropDismiss: false
    });
    await modal.present();
  }

  /**
   * 清理几何数据中的无效坐标
   */
  private cleanGeomCoordinates(geomData: any): any {
    if (geomData.coordinates && Array.isArray(geomData.coordinates)) {
      geomData.coordinates = geomData.coordinates.filter((coord: number[]) => {
        return coord[0] !== 0 && coord[1] !== 0; // 过滤掉经度或纬度为0的坐标
      });
    }
    return geomData;
  }

  /**
   * 检测未上传数据
   */
  async checkData(): Promise<void> {
    try {
      this.onUploadLength = (await this.backgroundGeolocation.getValidLocations() as BackgroundGeolocationResponse[]).length;
      this.locationConfig = await this.backgroundGeolocation.getConfig() as BackgroundGeolocationConfig;
      if (this.onUploadLength > 0) {
        // 将未发布到服务器的位置进行上传
        this.backgroundGeolocation.forceSync();
      }
    } catch (error) {
      console.error('检测未上传数据失败:', error);
      this.onUploadLength = 0;
    }
  }
  /**
   * 时间转换器
   */
  dateTransform(date: any, format: string): any {
    return this.datePipe.transform(date ? new Date(date) : new Date(), format);
  }

  /**
   * 消息提醒
   */
  async onToast(mgs = '出现错误请重试！', color = 'danger'): Promise<void> {
    const toast = await this.toastCtrl.create({
      message: mgs,
      duration: 2000,
      color
    });
    toast.present();
  }

  /**
   * 任务列表跟踪函数
   */
  trackByTaskCode(index: number, task: Task): string {
    return task.taskCode;
  }

  /**
   * 判断是否为活跃任务状态
   */
  isActiveTask(status: string): boolean {
    return ['未执行', '执行中'].includes(status);
  }

  // 回收
  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}
