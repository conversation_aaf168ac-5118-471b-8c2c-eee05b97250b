import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ShareModuleService } from 'src/app/share/share.service';
import { ModalController } from '@ionic/angular';
import { CameraLayerComponent } from 'src/app/share/map-component/camera-layer/camera-layer.component';

interface Camera {
  websiteName: string;
  name: string;
  id: string;
}

/**
 * 摄像头列表
 */
@Component({
  selector: 'app-camera-list',
  templateUrl: './camera-list.component.html',
  styleUrls: ['./camera-list.component.scss']
})
export class CameraListComponent implements OnInit, OnDestroy {
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  allCameras: Camera[] = [];

  constructor(
    private netSer: ShareModuleService,
    private modalCtrl: ModalController
  ) { }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit() {
    this.getCameras();
  }

  /**
   * 获取摄像头列表
   * @param name 搜索关键字，可选参数
   */
  getCameras(name?: string): void {
    const params = name ? { name } : {};
    this.netSer.getRequest({
      interfaceUrl: '/work-inspect/api/v2/inspect/camera/list',
      ...params
    })
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        if (res && res.data) {
          this.allCameras = res.data;
        }
      })
  }

  /**
   * 搜索框输入事件处理函数
   * @param event 输入事件
   */
  filterItems(event: any) {
    const searchText = event.detail?.value?.toLowerCase() || '';
    this.getCameras(searchText);
  }

  /**
   * 查看摄像头详情
   * @param camera 摄像头信息
   */
  async viewCameraDetails(camera: Camera) {
    // 先关闭当前窗口
    await this.modalCtrl.dismiss();
    
    // 创建并配置新的模态框
    const newModal = await this.modalCtrl.create({
      component: CameraLayerComponent,
      componentProps: { modelInfo: camera },
      showBackdrop: false,
      cssClass: ['app-camera-layer', 'allow-events-through'],
    });

    await newModal.present();
  }

  /**
   * 关闭模态框
   */
  close() {
    this.modalCtrl.dismiss();
  }
}
