import { ChangeDetectorRef, Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { MapComponent } from '../../share/map-component/map/map.component';

// 关键点数据模型
export interface KeyPoint {
  id: string;
  pointCode?: string;
  pointName: string;
  name: string;
  type: 'vehicle' | 'person';
  coordinate?: [number, number];
  isRainyDay: boolean;
  isItRaining: string;
  state: string;
  status?: 'inspected' | 'pending';
  point: string;
  bufferRange: number;
  geom?: any;
  lastInspectionTime?: string;
  description?: string;
}

// 筛选条件模型
export interface FilterOptions {
  inspectionType: 'all' | 'vehicle' | 'person';
  showRainyDay: boolean;
  depCode?: string;
  pointName?: string;
}

@Component({
  selector: 'app-keypoint-view',
  templateUrl: './keypoint-view.page.html',
  styleUrls: ['./keypoint-view.page.scss'],
})
export class KeypointViewPage implements OnInit, OnDestroy {
  @ViewChild('mapComponent') mapComponent: MapComponent;

  selectedTab = 'map';

  filter: FilterOptions = {
    inspectionType: 'all',
    showRainyDay: false
  };

  layerIds: string[] = ['p_pipe_joint_info', 'inspect_point'];
  loading = false;
  legendItems = [
    {
      type: 'vehicle',
      label: '巡视',
      color: '#007bff',
      icon: 'radio-button-off'
    },
    {
      type: 'person', 
      label: '巡查',
      color: '#28a745',
      icon: 'radio-button-off'
    },
    {
      type: 'rainy',
      label: '雨天不巡',
      color: '#ffc107',
      icon: 'radio-button-off'
    }
  ];

  private destroy$ = new Subject<void>();

  constructor(
    private nav: NavController,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.updateLayerIds(this.filter);
    setTimeout(() => {
      this.fitToDefaultExtent();
    }, 500);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  goBack(): void {
    this.nav.back();
  }

  onTabChange(event: any) {
    this.selectedTab = event.detail.value;

    if (this.selectedTab === 'map' && this.mapComponent) {
      setTimeout(() => {
        this.forceRefreshMapLayers();
      }, 100);
    }
  }

  onFilterChange(filter: Partial<FilterOptions>) {
    this.filter = { ...this.filter, ...filter };
    this.updateLayerIds(this.filter);

    if (this.mapComponent) {
      setTimeout(() => {
        this.forceRefreshMapLayers();
      }, 100);
    }

    this.cdr.detectChanges();
  }

  onLayerExtentRequest(layerId: string) {
    if (this.mapComponent) {
      this.forceRefreshMapLayers();
    }
  }


  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event: any): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.goBack();
    });
  }
  /**
   * 强制刷新地图图层
   */
  private forceRefreshMapLayers(): void {
    if (!this.mapComponent || !this.mapComponent.map) {
      return;
    }

    try {
      this.mapComponent.layerIds = [...this.layerIds];
      const map = this.mapComponent.map;
      this.mapComponent.businessLayerList.clear();
      this.mapComponent['initBusinessLayers'](this.layerIds);
      map.updateSize();
      map.renderSync();

      setTimeout(() => {
        this.fitToDefaultExtent();
      }, 500);
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 移动视野到固定范围
   */
  private fitToDefaultExtent(): void {
    try {
      if (!this.mapComponent || !this.mapComponent.map) {
        return;
      }

      const view = this.mapComponent.map.getView();
      const defaultExtent = [112.16695404052734, 35.32004928588867, 113.22920989990234, 36.11748504638672];

      view.fit(defaultExtent, {
        duration: 1000,
        padding: [50, 50, 50, 50],
        maxZoom: 16
      });
    } catch (error) {
      // 静默处理错误
    }
  }

  private updateLayerIds(filter: FilterOptions): void {
    const baseLayerIds = ['p_pipe_joint_info'];

    switch (filter.inspectionType) {
      case 'vehicle':
        this.layerIds = [...baseLayerIds, 'inspect_vehicle'];
        break;
      case 'person':
        this.layerIds = [...baseLayerIds, 'inspect_person'];
        break;
      default:
        this.layerIds = [...baseLayerIds, 'inspect_point'];
        break;
    }
  }
}