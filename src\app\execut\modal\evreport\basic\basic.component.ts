import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Geolocation } from '@ionic-native/geolocation/ngx';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailsMode } from 'src/app/@core/base/environment';
import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { EvReportInfo } from '../../../class/evreport';
import { ExecutService } from '../../../execut.service';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';

@Component({
  selector: 'evreport-basic',
  templateUrl: './basic.component.html',
  styleUrls: ['./basic.component.scss']
})
export class BasicComponent implements OnInit, OnDestroy {
  @Input() coordinate: any;
  // 数据模式
  @Input() modelMode: DetailsMode;
  // 数据模型
  @Input() modelInfo: EvReportInfo = new EvReportInfo();
  @Output() eventCodeChange = new EventEmitter<any>();
  // 表单
  infoFormGroup: FormGroup;
  DetailsMode: typeof DetailsMode = DetailsMode;
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  // 图片上传状态
  isImageUploading = false;
  base64Images: string[] = [];
  eventTypeUrl = '/work-inspect/api/v2/inspect/dict/msg/byDictCode?dictCode=eventType';
  constructor(
    private fb: FormBuilder, private dataSyncManager: DataSyncManagerService,
    private netSer: ExecutService, private UserInfoSer: UserInfoService,
    private toastSer: ToastService, private geolocation: Geolocation,
  ) { }

  ngOnInit() {
    this.initializeGeolocation();
    this.initializeForm();
  }

  /**
   * 初始化地理位置
   */
  private initializeGeolocation(): void {
    if (this.modelMode !== DetailsMode.ADD) {
      return;
    }

    if (this.isValidCoordinate(this.coordinate)) {
      this.modelInfo.eventGeom = this.coordinate;
      return;
    }

    this.getCurrentPosition();
  }

  /**
   * 检查坐标是否有效
   * @param coordinate 坐标
   * @returns 是否有效
   */
  private isValidCoordinate(coordinate: any): boolean {
    return coordinate &&
      !(Array.isArray(coordinate) && coordinate[0] === 0 && coordinate[1] === 0);
  }

  /**
   * 获取当前位置
   */
  private getCurrentPosition(): void {
    this.geolocation.getCurrentPosition().then((resp) => {
      this.modelInfo.eventGeom = [resp.coords.longitude, resp.coords.latitude];
    }).catch((error) => {
      console.log('获取位置失败 (Ionic Native)', error);
    });
  }

  private initializeForm(): void {
    this.infoFormGroup = this.fb.group({
      depCode: [this.modelInfo.depCode, [Validators.required]],
      depName: [this.modelInfo.depName],
      pipelineCode: [this.modelInfo.pipelineCode, [Validators.required]],
      pipelineName: [this.modelInfo.pipelineName],
      eventType: [this.modelInfo.eventType, [Validators.required]],
      eventDesc: [this.modelInfo.eventDesc],
    });
  }

  setHeight() {
    const bottomBtn = this.modelMode === DetailsMode.SEE ? 0 : 60;
    return `Calc(100% - ${bottomBtn}px)`
  }

  /**
   * 部门值改变
   */
  depChange(ev: any) {
    this.infoFormGroup.get('depName').setValue(ev.name);
    this.modelInfo.depName = ev.name;
  }

  /**
   * 管线值改变
   */
  pipelineChange(ev: any) {
    if (ev && ev.name) {
      this.infoFormGroup.get('pipelineName').setValue(ev.name);
      this.modelInfo.pipelineName = ev.name;
    }
  }

  /**
   * 处理图片数据
   * @param images 图片数据
   */
  getFileCodeChange(images: any) {
    this.modelInfo.picCode = images;
  }

  /**
   * 处理图片上传状态变化
   * @param isUploading 是否正在上传
   */
  onImageUploadStatusChange(isUploading: boolean) {
    this.isImageUploading = isUploading;
  }

  onBase64ImagesChange(images: string[]) {
    this.base64Images = images;
  }

  /**
   * 保存或更新事件报告
   */
  onSave() {
    if (this.validateForm()) {
      const formData = this.prepareFormData();
      this.submitForm(formData);
    }
  }

  /**
   * 验证表单
   */
  private validateForm(): boolean {
    if (!this.infoFormGroup.valid) {
      this.toastSer.presentToast('请填写必填项', 'danger');
      return false;
    }

    if (this.isImageUploading) {
      this.toastSer.presentToast('图片正在上传中，请稍后再试', 'warning');
      return false;
    }

    return true;
  }

  /**
   * 准备表单数据
   */
  private prepareFormData(): any {
    return {
      ...this.infoFormGroup.value,
      picCode: this.base64Images,
      eventGeom: this.modelInfo.eventGeom,
      eventCode: this.modelInfo.eventCode
    };
  }

  private async cacheInspectionData(formData: any) {

    const result = await this.dataSyncManager.addToCache(
      SyncDataType.INSPECTION_REPORT,
      {
        ...formData,
        userCode: this.UserInfoSer.userId,
        depCode: this.UserInfoSer.depCode,
      },
      `${ResourceGlobalConfig.url}/work-inspect/api/v2/inspect/batch/event/msg`
    );
    if (result.status === 'uploaded' && result.code === 0) {
      this.toastSer.presentToast('上报成功', 'success');
    } else if (result.status === 'cached') {
      this.toastSer.presentToast('数据已缓存，网络恢复后自动上传', 'success');
    }
  }

  /**
   * 提交表单数据
   */
  private submitForm(formData: any): void {
    const operation = this.modelMode === DetailsMode.ADD
      ? this.netSer.saveEReport([formData])
      : this.netSer.updateEReport(formData);

    operation
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => this.handleSubmitResponse(res),
        error: async err => {
          // 网络异常或接口失败时，自动缓存
          await this.cacheInspectionData(formData);
        }
      });
  }

  /**
   * 处理提交响应
   */
  private handleSubmitResponse(res: any): void {
    if (res.code === 0) {
      this.toastSer.presentToast('保存成功', 'success');
      this.modelMode = DetailsMode.SEE;
      const basicInfo = {
        ...res.data,
        eventGeom: this.modelInfo.eventGeom // 使用modelInfo中的最新坐标值
      };
      this.eventCodeChange.emit(basicInfo);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}

