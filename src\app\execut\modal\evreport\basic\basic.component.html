<form [formGroup]="infoFormGroup" (ngSubmit)="onSave()">
  <ion-content [style.height]="setHeight()">
    <ost-form-list [formGroup]="infoFormGroup" [modelMode]="modelMode">
      <ost-form-item>
        <ion-label>事件类型:</ion-label>
        <ost-input-search
          style="margin: 0px;"
          [readonly]="modelMode === DetailsMode.SEE"
          placeholder="请选择事项类型" slot="end"
          icon="caret-down-outline"
          [name]="modelInfo.eventType"
          formControlName="eventType"
        >
          <ost-option-source 
            [interfaceUrl]="eventTypeUrl"
            labelName="dictValue"
            labelValue="dictValue"
            [radioValue]="modelInfo.eventType ?? '管道占压'"
          ></ost-option-source>
        </ost-input-search>
        <ost-error errorCode="required">事件类型不能为空</ost-error>
      </ost-form-item>

      <ost-form-item>
        <ion-label>部门名称:</ion-label>
        <ost-input-search
          style="margin: 0px;"
          [readonly]="modelMode === DetailsMode.SEE"
          placeholder="点击选择部门"
          (valueChange)="depChange($event)"
          [name]="modelInfo.depName"
          formControlName="depCode" slot="end"
        >
          <search-source-org [depCode]="modelInfo.depCode"></search-source-org>
        </ost-input-search>
        <ost-error errorCode="required">部门名称不能为空</ost-error>
      </ost-form-item>

      <ost-form-item>
        <ion-label>管线名称:</ion-label>
        <ost-input-search
          style="margin: 0px;"
          [readonly]="modelMode === DetailsMode.SEE"
          placeholder="点击选择管线"
          [name]="modelInfo.pipelineName"
          (valueChange)="pipelineChange($event)"
          formControlName="pipelineCode" slot="end"
        >
          <search-source-pipe
            [depCode]="infoFormGroup.get('depCode').value"
            [interfaceUrl]="'/work-basic/api/v2/basic/pipeline/tree'">
          </search-source-pipe>
        </ost-input-search>
        <ost-error errorCode="required">管线名称不能为空</ost-error>
      </ost-form-item>
      
      <!-- 事件详情 -->
      <app-event-detail 
        [eventType]="infoFormGroup.get('eventType').value"
        [modelMode]="modelMode"
        [initialValues]="modelInfo"
        [pipelineId]="infoFormGroup.get('pipelineCode').value">
      </app-event-detail>

      <ost-form-item [divLine]="true" [required]="false">
        <ion-label>事件描述：</ion-label>
        <ion-textarea 
          [readonly]="modelMode === DetailsMode.SEE"
          formControlName="eventDesc" rows="3">
        </ion-textarea>
      </ost-form-item>
    </ost-form-list>

    <div>
      <ost-images
        [modelMode]="modelMode"
        [fileCodes]="modelInfo.picCode"
        [uploadMode]="'base64'"
        [base64Images]="base64Images"
        (base64ImagesChange)="onBase64ImagesChange($event)"
      >
      </ost-images>
    </div>

  </ion-content>

  <ion-footer *ngIf="modelMode !== DetailsMode.SEE">
    <ion-toolbar>
      <ion-button
        type="submit"
        color="primary"
        expand="block"
        [disabled]="!infoFormGroup.valid || isImageUploading">
        {{ isImageUploading ? '图片上传中...' : '保存' }}
      </ion-button>
    </ion-toolbar>
  </ion-footer>
</form>