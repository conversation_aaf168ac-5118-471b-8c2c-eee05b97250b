# Angular OpenLayers 公共组件设计方案

## 设计目标

- **低耦合**: 组件间依赖最小化，通过接口和抽象层解耦
- **高内聚**: 相关功能聚合在一起，职责清晰
- **灵活扩展**: 支持插件化架构，易于功能扩展
- **科学封装**: 合理的抽象层次，避免过度封装

## 整体架构设计

### 1. 分层架构

```
┌─────────────────────────────────────┐
│           应用层 (App Layer)          │
│  ┌─────────────┐ ┌─────────────┐    │
│  │  业务组件    │ │  页面组件    │    │
│  └─────────────┘ └─────────────┘    │
├─────────────────────────────────────┤
│         组件层 (Component Layer)      │
│  ┌─────────────┐ ┌─────────────┐    │
│  │  地图容器    │ │  地图控件    │    │
│  └─────────────┘ └─────────────┘    │
├─────────────────────────────────────┤
│          服务层 (Service Layer)       │
│  ┌─────────────┐ ┌─────────────┐    │
│  │  地图服务    │ │  图层服务    │    │
│  └─────────────┘ └─────────────┘    │
├─────────────────────────────────────┤
│          适配层 (Adapter Layer)       │
│  ┌─────────────┐ ┌─────────────┐    │
│  │ OpenLayers  │ │  其他地图库   │    │
│  │   适配器     │ │    适配器     │    │
│  └─────────────┘ └─────────────┘    │
└─────────────────────────────────────┘
```

### 2. 核心模块划分

```typescript
// 核心模块结构
export interface MapModules {
  core: MapCoreModule;           // 核心地图功能
  layers: LayerModule;           // 图层管理
  controls: ControlModule;       // 地图控件
  interactions: InteractionModule; // 交互功能
  plugins: PluginModule;         // 插件系统
  utils: UtilModule;             // 工具函数
}
```

## 核心接口设计

### 1. 地图核心接口

```typescript
// 地图配置接口
export interface MapConfig {
  container: string | HTMLElement;
  center?: [number, number];
  zoom?: number;
  projection?: string;
  extent?: [number, number, number, number];
  minZoom?: number;
  maxZoom?: number;
  enableRotation?: boolean;
  constrainRotation?: boolean;
}

// 地图实例接口
export interface IMapInstance {
  readonly id: string;
  readonly config: MapConfig;
  readonly view: IMapView;
  readonly layers: ILayerManager;
  readonly controls: IControlManager;
  readonly interactions: IInteractionManager;
  
  // 核心方法
  render(): void;
  destroy(): void;
  resize(): void;
  
  // 事件系统
  on<T = any>(event: string, handler: (data: T) => void): void;
  off(event: string, handler?: Function): void;
  emit<T = any>(event: string, data?: T): void;
}

// 地图视图接口
export interface IMapView {
  getCenter(): [number, number];
  setCenter(center: [number, number], options?: ViewOptions): void;
  getZoom(): number;
  setZoom(zoom: number, options?: ViewOptions): void;
  getExtent(): [number, number, number, number];
  fit(extent: [number, number, number, number], options?: FitOptions): void;
  animate(options: AnimationOptions): void;
}

// 视图操作选项
export interface ViewOptions {
  duration?: number;
  easing?: (t: number) => number;
}

export interface FitOptions extends ViewOptions {
  padding?: [number, number, number, number];
  maxZoom?: number;
  minZoom?: number;
}

export interface AnimationOptions extends ViewOptions {
  center?: [number, number];
  zoom?: number;
  rotation?: number;
}
```

### 2. 图层管理接口

```typescript
// 图层基础接口
export interface ILayer {
  readonly id: string;
  readonly type: LayerType;
  readonly visible: boolean;
  readonly opacity: number;
  readonly zIndex: number;
  
  setVisible(visible: boolean): void;
  setOpacity(opacity: number): void;
  setZIndex(zIndex: number): void;
  getExtent(): [number, number, number, number] | null;
  refresh(): void;
}

// 图层类型
export enum LayerType {
  TILE = 'tile',
  VECTOR = 'vector',
  IMAGE = 'image',
  GROUP = 'group'
}

// 图层管理器接口
export interface ILayerManager {
  add(layer: ILayer): void;
  remove(layerId: string): void;
  get(layerId: string): ILayer | null;
  getAll(): ILayer[];
  getByType(type: LayerType): ILayer[];
  clear(): void;
  
  // 图层排序
  moveToTop(layerId: string): void;
  moveToBottom(layerId: string): void;
  moveUp(layerId: string): void;
  moveDown(layerId: string): void;
}

// 图层配置接口
export interface LayerConfig {
  id: string;
  type: LayerType;
  name?: string;
  visible?: boolean;
  opacity?: number;
  zIndex?: number;
  extent?: [number, number, number, number];
  source: LayerSourceConfig;
}

// 图层数据源配置
export interface LayerSourceConfig {
  type: SourceType;
  url?: string;
  urls?: string[];
  params?: Record<string, any>;
  projection?: string;
  crossOrigin?: string;
  [key: string]: any;
}

export enum SourceType {
  XYZ = 'xyz',
  WMS = 'wms',
  WMTS = 'wmts',
  VECTOR = 'vector',
  OSM = 'osm'
}
```

### 3. 控件系统接口

```typescript
// 控件基础接口
export interface IControl {
  readonly id: string;
  readonly type: ControlType;
  readonly element: HTMLElement;
  
  render(): HTMLElement;
  destroy(): void;
  setVisible(visible: boolean): void;
}

// 控件类型
export enum ControlType {
  ZOOM = 'zoom',
  ATTRIBUTION = 'attribution',
  SCALE = 'scale',
  OVERVIEW = 'overview',
  FULLSCREEN = 'fullscreen',
  CUSTOM = 'custom'
}

// 控件管理器接口
export interface IControlManager {
  add(control: IControl, position?: ControlPosition): void;
  remove(controlId: string): void;
  get(controlId: string): IControl | null;
  getAll(): IControl[];
  clear(): void;
}

// 控件位置
export enum ControlPosition {
  TOP_LEFT = 'top-left',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_RIGHT = 'bottom-right',
  TOP_CENTER = 'top-center',
  BOTTOM_CENTER = 'bottom-center'
}
```

### 4. 交互系统接口

```typescript
// 交互基础接口
export interface IInteraction {
  readonly id: string;
  readonly type: InteractionType;
  readonly active: boolean;
  
  setActive(active: boolean): void;
  destroy(): void;
}

// 交互类型
export enum InteractionType {
  DRAW = 'draw',
  MODIFY = 'modify',
  SELECT = 'select',
  DRAG = 'drag',
  MEASURE = 'measure',
  CUSTOM = 'custom'
}

// 交互管理器接口
export interface IInteractionManager {
  add(interaction: IInteraction): void;
  remove(interactionId: string): void;
  get(interactionId: string): IInteraction | null;
  getAll(): IInteraction[];
  getActive(): IInteraction[];
  clear(): void;
  
  // 交互状态管理
  activateOnly(interactionId: string): void;
  deactivateAll(): void;
}
```

## 核心组件实现

### 1. 地图容器组件

```typescript
@Component({
  selector: 'ng-map',
  template: `
    <div #mapContainer class="map-container" [style.height]="height">
      <ng-content></ng-content>
      
      <!-- 加载状态 -->
      <div *ngIf="loading()" class="map-loading">
        <ion-spinner></ion-spinner>
        <span>地图加载中...</span>
      </div>
      
      <!-- 错误状态 -->
      <div *ngIf="error()" class="map-error">
        <ion-icon name="warning"></ion-icon>
        <span>{{ error() }}</span>
        <ion-button (click)="retry()">重试</ion-button>
      </div>
    </div>
  `,
  styleUrls: ['./map.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, IonicModule],
  providers: [
    { provide: MAP_CONFIG, useFactory: () => inject(MapComponent).config },
    MapService,
    LayerService,
    ControlService,
    InteractionService
  ]
})
export class MapComponent implements OnInit, OnDestroy, AfterViewInit {
  // 输入属性 - 使用信号
  readonly config = input<Partial<MapConfig>>({});
  readonly height = input<string>('400px');
  readonly plugins = input<string[]>([]);
  
  // 输出事件
  readonly mapReady = output<IMapInstance>();
  readonly mapClick = output<MapClickEvent>();
  readonly mapMove = output<MapMoveEvent>();
  readonly layerChange = output<LayerChangeEvent>();
  
  // 状态信号
  readonly loading = signal<boolean>(true);
  readonly error = signal<string | null>(null);
  readonly mapInstance = signal<IMapInstance | null>(null);
  
  // 视图引用
  @ViewChild('mapContainer', { static: true })
  private readonly mapContainer!: ElementRef<HTMLDivElement>;
  
  // 依赖注入
  private readonly destroyRef = inject(DestroyRef);
  private readonly mapService = inject(MapService);
  private readonly pluginService = inject(PluginService);
  
  ngOnInit(): void {
    // 响应配置变化
    effect(() => {
      const config = this.config();
      if (this.mapInstance()) {
        this.updateMapConfig(config);
      }
    });
  }
  
  ngAfterViewInit(): void {
    this.initializeMap();
  }
  
  ngOnDestroy(): void {
    this.destroyMap();
  }
  
  private async initializeMap(): Promise<void> {
    try {
      this.loading.set(true);
      this.error.set(null);
      
      // 等待容器准备就绪
      await this.waitForContainer();
      
      // 创建地图配置
      const mapConfig: MapConfig = {
        container: this.mapContainer.nativeElement,
        center: [0, 0],
        zoom: 2,
        projection: 'EPSG:3857',
        ...this.config()
      };
      
      // 创建地图实例
      const mapInstance = await this.mapService.createMap(mapConfig);
      
      // 加载插件
      await this.loadPlugins(mapInstance);
      
      // 设置事件监听
      this.setupEventListeners(mapInstance);
      
      // 更新状态
      this.mapInstance.set(mapInstance);
      this.loading.set(false);
      
      // 发出就绪事件
      this.mapReady.emit(mapInstance);
      
    } catch (err) {
      this.error.set(err instanceof Error ? err.message : '地图初始化失败');
      this.loading.set(false);
    }
  }
  
  private async waitForContainer(): Promise<void> {
    return new Promise((resolve) => {
      if (this.mapContainer?.nativeElement) {
        requestAnimationFrame(() => resolve());
      } else {
        setTimeout(() => this.waitForContainer().then(resolve), 50);
      }
    });
  }
  
  private async loadPlugins(mapInstance: IMapInstance): Promise<void> {
    const pluginNames = this.plugins();
    if (pluginNames.length > 0) {
      await this.pluginService.loadPlugins(mapInstance, pluginNames);
    }
  }
  
  private setupEventListeners(mapInstance: IMapInstance): void {
    // 地图点击事件
    mapInstance.on<MapClickEvent>('click', (event) => {
      this.mapClick.emit(event);
    });
    
    // 地图移动事件
    mapInstance.on<MapMoveEvent>('moveend', (event) => {
      this.mapMove.emit(event);
    });
    
    // 图层变化事件
    mapInstance.layers.on?.('change', (event) => {
      this.layerChange.emit(event);
    });
  }
  
  private updateMapConfig(config: Partial<MapConfig>): void {
    const mapInstance = this.mapInstance();
    if (!mapInstance) return;
    
    // 更新视图配置
    if (config.center) {
      mapInstance.view.setCenter(config.center);
    }
    if (config.zoom !== undefined) {
      mapInstance.view.setZoom(config.zoom);
    }
  }
  
  private destroyMap(): void {
    const mapInstance = this.mapInstance();
    if (mapInstance) {
      mapInstance.destroy();
      this.mapInstance.set(null);
    }
  }
  
  public retry(): void {
    this.destroyMap();
    this.initializeMap();
  }
  
  // 公共API
  public getMapInstance(): IMapInstance | null {
    return this.mapInstance();
  }
}
```

### 2. 图层组件

```typescript
@Component({
  selector: 'ng-map-layer',
  template: '',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MapLayerComponent implements OnInit, OnDestroy {
  // 输入属性
  readonly config = input.required<LayerConfig>();
  readonly visible = input<boolean>(true);
  readonly opacity = input<number>(1);
  readonly zIndex = input<number>(0);
  
  // 输出事件
  readonly layerReady = output<ILayer>();
  readonly layerError = output<Error>();
  
  // 状态
  readonly layer = signal<ILayer | null>(null);
  
  // 依赖注入
  private readonly mapComponent = inject(MapComponent);
  private readonly layerService = inject(LayerService);
  private readonly destroyRef = inject(DestroyRef);
  
  ngOnInit(): void {
    // 等待地图就绪
    effect(() => {
      const mapInstance = this.mapComponent.mapInstance();
      if (mapInstance && !this.layer()) {
        this.createLayer(mapInstance);
      }
    });
    
    // 响应属性变化
    effect(() => {
      const layer = this.layer();
      if (layer) {
        layer.setVisible(this.visible());
        layer.setOpacity(this.opacity());
        layer.setZIndex(this.zIndex());
      }
    });
  }
  
  ngOnDestroy(): void {
    this.removeLayer();
  }
  
  private async createLayer(mapInstance: IMapInstance): Promise<void> {
    try {
      const layerConfig = this.config();
      const layer = await this.layerService.createLayer(layerConfig);
      
      // 设置初始属性
      layer.setVisible(this.visible());
      layer.setOpacity(this.opacity());
      layer.setZIndex(this.zIndex());
      
      // 添加到地图
      mapInstance.layers.add(layer);
      
      // 更新状态
      this.layer.set(layer);
      this.layerReady.emit(layer);
      
    } catch (error) {
      this.layerError.emit(error as Error);
    }
  }
  
  private removeLayer(): void {
    const layer = this.layer();
    const mapInstance = this.mapComponent.mapInstance();
    
    if (layer && mapInstance) {
      mapInstance.layers.remove(layer.id);
      this.layer.set(null);
    }
  }
  
  // 公共API
  public getLayer(): ILayer | null {
    return this.layer();
  }
  
  public refresh(): void {
    const layer = this.layer();
    if (layer) {
      layer.refresh();
    }
  }
}
```

### 3. 控件组件

```typescript
@Component({
  selector: 'ng-map-control',
  template: `
    <div #controlElement class="map-control" [class]="cssClass()">
      <ng-content></ng-content>
    </div>
  `,
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MapControlComponent implements OnInit, OnDestroy {
  // 输入属性
  readonly type = input<ControlType>(ControlType.CUSTOM);
  readonly position = input<ControlPosition>(ControlPosition.TOP_RIGHT);
  readonly visible = input<boolean>(true);
  readonly cssClass = input<string>('');
  
  // 输出事件
  readonly controlReady = output<IControl>();
  
  // 视图引用
  @ViewChild('controlElement', { static: true })
  private readonly controlElement!: ElementRef<HTMLDivElement>;
  
  // 状态
  readonly control = signal<IControl | null>(null);
  
  // 依赖注入
  private readonly mapComponent = inject(MapComponent);
  private readonly controlService = inject(ControlService);
  
  ngOnInit(): void {
    // 等待地图就绪
    effect(() => {
      const mapInstance = this.mapComponent.mapInstance();
      if (mapInstance && !this.control()) {
        this.createControl(mapInstance);
      }
    });
    
    // 响应可见性变化
    effect(() => {
      const control = this.control();
      if (control) {
        control.setVisible(this.visible());
      }
    });
  }
  
  ngOnDestroy(): void {
    this.removeControl();
  }
  
  private createControl(mapInstance: IMapInstance): void {
    const control = this.controlService.createControl({
      type: this.type(),
      element: this.controlElement.nativeElement
    });
    
    // 添加到地图
    mapInstance.controls.add(control, this.position());
    
    // 更新状态
    this.control.set(control);
    this.controlReady.emit(control);
  }
  
  private removeControl(): void {
    const control = this.control();
    const mapInstance = this.mapComponent.mapInstance();
    
    if (control && mapInstance) {
      mapInstance.controls.remove(control.id);
      this.control.set(null);
    }
  }
}
```

## 服务层实现

### 1. 地图服务

```typescript
@Injectable({ providedIn: 'root' })
export class MapService {
  private readonly mapInstances = new Map<string, IMapInstance>();
  private readonly adapters = new Map<string, IMapAdapter>();
  
  constructor() {
    // 注册默认适配器
    this.registerAdapter('openlayers', new OpenLayersAdapter());
  }
  
  public registerAdapter(name: string, adapter: IMapAdapter): void {
    this.adapters.set(name, adapter);
  }
  
  public async createMap(config: MapConfig, adapterName = 'openlayers'): Promise<IMapInstance> {
    const adapter = this.adapters.get(adapterName);
    if (!adapter) {
      throw new Error(`地图适配器 '${adapterName}' 未找到`);
    }
    
    const mapInstance = await adapter.createMap(config);
    this.mapInstances.set(mapInstance.id, mapInstance);
    
    return mapInstance;
  }
  
  public getMap(id: string): IMapInstance | null {
    return this.mapInstances.get(id) || null;
  }
  
  public destroyMap(id: string): void {
    const mapInstance = this.mapInstances.get(id);
    if (mapInstance) {
      mapInstance.destroy();
      this.mapInstances.delete(id);
    }
  }
}
```

### 2. 图层服务

```typescript
@Injectable({ providedIn: 'root' })
export class LayerService {
  private readonly layerFactories = new Map<LayerType, ILayerFactory>();
  
  constructor() {
    // 注册图层工厂
    this.registerFactory(LayerType.TILE, new TileLayerFactory());
    this.registerFactory(LayerType.VECTOR, new VectorLayerFactory());
    this.registerFactory(LayerType.IMAGE, new ImageLayerFactory());
    this.registerFactory(LayerType.GROUP, new GroupLayerFactory());
  }
  
  public registerFactory(type: LayerType, factory: ILayerFactory): void {
    this.layerFactories.set(type, factory);
  }
  
  public async createLayer(config: LayerConfig): Promise<ILayer> {
    const factory = this.layerFactories.get(config.type);
    if (!factory) {
      throw new Error(`图层类型 '${config.type}' 不支持`);
    }
    
    return await factory.create(config);
  }
  
  public validateConfig(config: LayerConfig): boolean {
    // 配置验证逻辑
    return true;
  }
}
```

## 插件系统

### 1. 插件接口

```typescript
// 插件基础接口
export interface IMapPlugin {
  readonly name: string;
  readonly version: string;
  readonly dependencies?: string[];
  
  install(mapInstance: IMapInstance): Promise<void> | void;
  uninstall(mapInstance: IMapInstance): Promise<void> | void;
}

// 插件管理器
@Injectable({ providedIn: 'root' })
export class PluginService {
  private readonly plugins = new Map<string, IMapPlugin>();
  private readonly installedPlugins = new Map<string, Set<string>>();
  
  public registerPlugin(plugin: IMapPlugin): void {
    this.plugins.set(plugin.name, plugin);
  }
  
  public async loadPlugins(mapInstance: IMapInstance, pluginNames: string[]): Promise<void> {
    const mapId = mapInstance.id;
    const installed = this.installedPlugins.get(mapId) || new Set();
    
    for (const pluginName of pluginNames) {
      if (!installed.has(pluginName)) {
        await this.loadPlugin(mapInstance, pluginName);
        installed.add(pluginName);
      }
    }
    
    this.installedPlugins.set(mapId, installed);
  }
  
  private async loadPlugin(mapInstance: IMapInstance, pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new Error(`插件 '${pluginName}' 未找到`);
    }
    
    // 检查依赖
    if (plugin.dependencies) {
      await this.loadPlugins(mapInstance, plugin.dependencies);
    }
    
    // 安装插件
    await plugin.install(mapInstance);
  }
}
```

### 2. 示例插件

```typescript
// 测量工具插件
export class MeasurePlugin implements IMapPlugin {
  readonly name = 'measure';
  readonly version = '1.0.0';
  
  async install(mapInstance: IMapInstance): Promise<void> {
    // 添加测量交互
    const measureInteraction = new MeasureInteraction({
      type: 'distance'
    });
    
    mapInstance.interactions.add(measureInteraction);
    
    // 添加测量控件
    const measureControl = new MeasureControl({
      interaction: measureInteraction
    });
    
    mapInstance.controls.add(measureControl, ControlPosition.TOP_LEFT);
  }
  
  async uninstall(mapInstance: IMapInstance): Promise<void> {
    // 移除相关组件
    mapInstance.interactions.remove('measure');
    mapInstance.controls.remove('measure');
  }
}
```

## 使用示例

### 1. 基础地图

```html
<ng-map 
  [config]="mapConfig"
  [height]="'500px'"
  (mapReady)="onMapReady($event)"
  (mapClick)="onMapClick($event)">
  
  <!-- 底图图层 -->
  <ng-map-layer 
    [config]="baseLayerConfig"
    [visible]="true">
  </ng-map-layer>
  
  <!-- 业务图层 -->
  <ng-map-layer 
    [config]="businessLayerConfig"
    [visible]="showBusinessLayer"
    [opacity]="0.8">
  </ng-map-layer>
  
  <!-- 缩放控件 -->
  <ng-map-control 
    [type]="ControlType.ZOOM"
    [position]="ControlPosition.TOP_LEFT">
  </ng-map-control>
  
  <!-- 自定义控件 -->
  <ng-map-control 
    [type]="ControlType.CUSTOM"
    [position]="ControlPosition.TOP_RIGHT">
    <ion-button (click)="toggleLayer()">
      <ion-icon name="layers"></ion-icon>
    </ion-button>
  </ng-map-control>
  
</ng-map>
```

### 2. 组件配置

```typescript
export class MapPageComponent {
  readonly mapConfig = signal<MapConfig>({
    center: [112.5504237, 37.8736249],
    zoom: 14,
    projection: 'EPSG:4326',
    minZoom: 1,
    maxZoom: 20
  });
  
  readonly baseLayerConfig = signal<LayerConfig>({
    id: 'base-layer',
    type: LayerType.TILE,
    name: '底图',
    source: {
      type: SourceType.XYZ,
      url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
    }
  });
  
  readonly businessLayerConfig = signal<LayerConfig>({
    id: 'business-layer',
    type: LayerType.TILE,
    name: '业务图层',
    source: {
      type: SourceType.WMS,
      url: 'https://example.com/geoserver/wms',
      params: {
        LAYERS: 'workspace:layer',
        FORMAT: 'image/png',
        TRANSPARENT: true
      }
    }
  });
  
  readonly showBusinessLayer = signal<boolean>(true);
  
  onMapReady(mapInstance: IMapInstance): void {
    console.log('地图已就绪', mapInstance);
  }
  
  onMapClick(event: MapClickEvent): void {
    console.log('地图点击', event.coordinate);
  }
  
  toggleLayer(): void {
    this.showBusinessLayer.update(visible => !visible);
  }
}
```

## 扩展指南

### 1. 自定义图层类型

```typescript
// 1. 定义图层接口
export interface IHeatmapLayer extends ILayer {
  setData(data: HeatmapData[]): void;
  setRadius(radius: number): void;
  setBlur(blur: number): void;
}

// 2. 实现图层类
export class HeatmapLayer implements IHeatmapLayer {
  // 实现接口方法
}

// 3. 创建图层工厂
export class HeatmapLayerFactory implements ILayerFactory {
  async create(config: LayerConfig): Promise<ILayer> {
    return new HeatmapLayer(config);
  }
}

// 4. 注册图层类型
layerService.registerFactory(LayerType.HEATMAP, new HeatmapLayerFactory());
```

### 2. 自定义控件

```typescript
// 1. 实现控件接口
export class CustomControl implements IControl {
  readonly id = 'custom-control';
  readonly type = ControlType.CUSTOM;
  readonly element: HTMLElement;
  
  constructor(private config: CustomControlConfig) {
    this.element = this.createElement();
  }
  
  render(): HTMLElement {
    return this.element;
  }
  
  destroy(): void {
    this.element.remove();
  }
  
  setVisible(visible: boolean): void {
    this.element.style.display = visible ? 'block' : 'none';
  }
  
  private createElement(): HTMLElement {
    // 创建控件元素
    const element = document.createElement('div');
    element.className = 'custom-control';
    return element;
  }
}
```

### 3. 自定义交互

```typescript
// 1. 实现交互接口
export class DrawInteraction implements IInteraction {
  readonly id = 'draw';
  readonly type = InteractionType.DRAW;
  private _active = false;
  
  get active(): boolean {
    return this._active;
  }
  
  setActive(active: boolean): void {
    this._active = active;
    // 实现激活/停用逻辑
  }
  
  destroy(): void {
    // 清理资源
  }
}
```

## 总结

这个设计方案具有以下特点：

1. **低耦合**: 通过接口和抽象层实现组件解耦
2. **高内聚**: 相关功能聚合在对应的模块中
3. **灵活扩展**: 支持插件系统和工厂模式扩展
4. **科学封装**: 合理的抽象层次，避免过度封装
5. **现代化**: 使用Angular最新特性（信号、独立组件等）
6. **类型安全**: 完整的TypeScript类型定义
7. **性能优化**: 内存管理和事件优化
8. **易于维护**: 清晰的架构和代码组织

通过这个设计，可以构建出功能强大、易于扩展的地图组件库。