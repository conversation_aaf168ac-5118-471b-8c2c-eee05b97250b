<div class="filter-panel">
  <!-- 巡检类型筛选 -->
  <div class="filter-group">
    <div class="filter-label">巡检类型:</div>
    <div class="filter-buttons">
      <ion-button 
        size="small" 
        [fill]="isInspectionTypeActive('all') ? 'solid' : 'outline'"
        [color]="isInspectionTypeActive('all') ? 'primary' : 'medium'"
        (click)="onInspectionTypeChange('all')">
        <ion-icon name="apps-outline" slot="start"></ion-icon>
        全部
      </ion-button>
      
      <ion-button 
        size="small" 
        [fill]="isInspectionTypeActive('vehicle') ? 'solid' : 'outline'"
        [color]="isInspectionTypeActive('vehicle') ? 'success' : 'medium'"
        (click)="onInspectionTypeChange('vehicle')">
        <ion-icon name="car-outline" slot="start"></ion-icon>
        巡视
      </ion-button>
      
      <ion-button 
        size="small" 
        [fill]="isInspectionTypeActive('person') ? 'solid' : 'outline'"
        [color]="isInspectionTypeActive('person') ? 'warning' : 'medium'"
        (click)="onInspectionTypeChange('person')">
        <ion-icon name="person-outline" slot="start"></ion-icon>
        巡查
      </ion-button>
    </div>
  </div>

</div>