import { Injectable } from '@angular/core';
import { SyncCacheManagerService } from '../providers/data-sync/sync-cache-manager.service';
import { SyncDataType, SyncCacheItem } from '../providers/data-sync/types/sync-data.types';
import { ToastService } from '../providers/toast.service';

/**
 * 测试数据模拟器服务
 * 用于生成模拟的同步失败数据，便于测试数据导出功能
 */
@Injectable({
  providedIn: 'root'
})
export class TestDataSimulatorService {

  constructor(
    private syncCacheManager: SyncCacheManagerService,
    private toastService: ToastService
  ) { }

  /**
   * 模拟同步失败数据 - 用于测试导出功能
   * @param userInfo 用户信息对象，包含 userId 和 depCode
   * @param onDataCountUpdate 数据数量更新回调函数
   */
  async simulateFailedSyncData(
    userInfo: { userId: string; depCode: string },
    onDataCountUpdate?: () => Promise<void>
  ): Promise<void> {
    try {
      // 首先清空现有的失败数据，确保测试环境干净
      await this.clearAllFailedData();

      const currentTime = Date.now();
      const maxRetryCount = 5; // 模拟达到最大重试次数的失败数据（系统最大重试次数是5）

      // 生成模拟数据
      const mockData = this.generateMockData(userInfo, currentTime);
      
      // 创建失败的缓存项
      const failedItems = this.createFailedCacheItems(mockData, currentTime, maxRetryCount);

      // 添加失败数据到缓存
      await this.addFailedDataToCache(failedItems, maxRetryCount);

      // 刷新失败数据数量显示
      if (onDataCountUpdate) {
        await onDataCountUpdate();
      }

      await this.toastService.presentToast(
        `已成功模拟 ${failedItems.length} 条同步失败数据，可以测试导出功能了！`,
        'success',
        3000,
        'middle'
      );

      this.logSimulationResults(mockData, failedItems);

    } catch (error) {
      console.error('模拟失败数据时出错:', error);
      await this.toastService.presentToast('模拟数据失败，请查看控制台错误信息', 'danger', 3000, 'middle');
    }
  }

  /**
   * 清空所有失败数据 - 用于测试
   */
  async clearAllFailedData(): Promise<void> {
    const types = Object.values(SyncDataType);
    for (const type of types) {
      await this.syncCacheManager.clearCache(type);
    }
  }

  /**
   * 生成模拟数据
   */
  private generateMockData(userInfo: { userId: string; depCode: string }, currentTime: number) {
    return {
      manualClockInData: [
        {
          taskCode: 'TASK_001',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          longitude: 108.123456,
          latitude: 34.567890,
          clockInTime: currentTime - 3600000, // 1小时前
          clockInType: 'manual',
          remark: '手动打卡测试数据1'
        },
        {
          taskCode: 'TASK_002',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          longitude: 108.234567,
          latitude: 34.678901,
          clockInTime: currentTime - 7200000, // 2小时前
          clockInType: 'manual',
          remark: '手动打卡测试数据2'
        }
      ],
      autoKeyPointData: [
        {
          taskCode: 'TASK_001',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          longitude: 108.345678,
          latitude: 34.789012,
          trajectoryTime: currentTime - 1800000, // 30分钟前
          keyPointName: '关键点A',
          autoClockIn: true
        },
        {
          taskCode: 'TASK_002',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          longitude: 108.456789,
          latitude: 34.890123,
          trajectoryTime: currentTime - 5400000, // 1.5小时前
          keyPointName: '关键点B',
          autoClockIn: true
        },
        {
          taskCode: 'TASK_003',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          longitude: 108.567890,
          latitude: 34.901234,
          trajectoryTime: currentTime - 9000000, // 2.5小时前
          keyPointName: '关键点C',
          autoClockIn: true
        }
      ],
      inspectionReportData: [
        {
          taskCode: 'TASK_001',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          eventType: 'equipment_abnormal',
          eventLevel: 'high',
          eventDescription: '设备异常测试事件1',
          longitude: 108.678901,
          latitude: 34.012345,
          reportTime: currentTime - 2700000, // 45分钟前
          images: ['image1.jpg', 'image2.jpg'],
          attachments: []
        },
        {
          taskCode: 'TASK_002',
          userCode: userInfo.userId,
          depCode: userInfo.depCode,
          eventType: 'safety_hazard',
          eventLevel: 'medium',
          eventDescription: '安全隐患测试事件2',
          longitude: 108.789012,
          latitude: 34.123456,
          reportTime: currentTime - 6300000, // 1小时45分钟前
          images: ['image3.jpg'],
          attachments: ['report.pdf']
        }
      ]
    };
  }

  /**
   * 创建失败的缓存项
   */
  private createFailedCacheItems(mockData: any, currentTime: number, maxRetryCount: number): SyncCacheItem[] {
    const failedItems: SyncCacheItem[] = [];

    // 添加手动打卡失败数据
    mockData.manualClockInData.forEach((data: any, index: number) => {
      failedItems.push({
        id: `manual_${currentTime}_${index}`,
        type: SyncDataType.MANUAL_CLOCK_IN,
        data,
        timestamp: currentTime - (index + 1) * 1800000, // 每个间隔30分钟
        retryCount: maxRetryCount,
        uploadUrl: '/work-inspect/api/v2/inspect/manual/clock-in',
        method: 'POST'
      });
    });

    // 添加自动关键点打卡失败数据
    mockData.autoKeyPointData.forEach((data: any, index: number) => {
      failedItems.push({
        id: `auto_keypoint_${currentTime}_${index}`,
        type: SyncDataType.AUTO_KEY_POINT_CLOCK_IN,
        data,
        timestamp: currentTime - (index + 1) * 1200000, // 每个间隔20分钟
        retryCount: maxRetryCount,
        uploadUrl: '/work-inspect/api/v2/inspect/auto/key-point/clock-in',
        method: 'POST'
      });
    });

    // 添加巡检事件上报失败数据
    mockData.inspectionReportData.forEach((data: any, index: number) => {
      failedItems.push({
        id: `inspection_${currentTime}_${index}`,
        type: SyncDataType.INSPECTION_REPORT,
        data,
        timestamp: currentTime - (index + 1) * 900000, // 每个间隔15分钟
        retryCount: maxRetryCount,
        uploadUrl: '/work-inspect/api/v2/inspect/batch/event/msg',
        method: 'POST'
      });
    });

    return failedItems;
  }

  /**
   * 添加失败数据到缓存
   */
  private async addFailedDataToCache(failedItems: SyncCacheItem[], maxRetryCount: number): Promise<void> {
    for (const item of failedItems) {
      // 先添加到缓存
      const cacheItem = await this.syncCacheManager.addToCache(
        item.type,
        item.data,
        item.uploadUrl,
        item.method || 'POST'
      );

      // 然后更新重试次数为最大值，使其成为失败数据
      cacheItem.retryCount = maxRetryCount;
      await this.syncCacheManager.updateCacheItems(item.type, [cacheItem]);
    }
  }

  /**
   * 记录模拟结果
   */
  private logSimulationResults(mockData: any, failedItems: SyncCacheItem[]): void {
    console.log('模拟失败数据详情:', {
      总数量: failedItems.length,
      手动打卡: mockData.manualClockInData.length,
      自动关键点打卡: mockData.autoKeyPointData.length,
      巡检事件上报: mockData.inspectionReportData.length,
      数据详情: failedItems
    });
  }
}
