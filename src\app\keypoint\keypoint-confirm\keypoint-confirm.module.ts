import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

// 导入路由模块
import { RouterModule } from '@angular/router';
import { KeypointConfirmPage } from './keypoint-confirm.page';
import { ConfirmListViewComponent } from './components/confirm-list-view/confirm-list-view.component';
import { ConfirmMapViewComponent } from './components/confirm-map-view/confirm-map-view.component';
import { ShareModule } from '../../share/share.module';
import { KeypointConfirmPageRoutingModule } from './keypoint-confirm-routing.module';
 

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    KeypointConfirmPageRoutingModule,
    ShareModule
  ],
  declarations: [
    KeypointConfirmPage,
    ConfirmListViewComponent,
    ConfirmMapViewComponent
  ]
})
export class KeypointConfirmPageModule {}