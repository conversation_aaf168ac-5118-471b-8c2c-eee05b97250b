import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ShareModule } from '../../share/share.module';

import { KeypointViewPageRoutingModule } from './keypoint-view-routing.module';
import { KeypointViewPage } from './keypoint-view.page';
import { MapFilterPanelComponent } from './components/map-filter-panel/map-filter-panel.component';
import { KeypointListViewComponent } from './components/keypoint-list-view/keypoint-list-view.component';
import { KeypointViewService } from './keypoint-view.service';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ShareModule,
    KeypointViewPageRoutingModule
  ],
  declarations: [
    KeypointViewPage,
    MapFilterPanelComponent,
    KeypointListViewComponent
  ],
  providers: [
    KeypointViewService
  ]
})
export class KeypointViewPageModule {}