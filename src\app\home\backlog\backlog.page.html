<ion-card-content class="backlog">
  <!-- 标题区域 -->
  <header class="backlog-header">
    <h3 class="backlog-title">巡检任务</h3>
    <time class="backlog-date">{{nowDate}}</time>
  </header>

  <!-- 内容区域 -->
  <main class="backlog-content">
    <!-- 加载状态 -->
    <div class="loading-state" *ngIf="isLoading">
      <ion-spinner name="crescent"></ion-spinner>
      <span class="loading-text">加载中...</span>
    </div>

    <!-- 任务列表 -->
    <div class="task-list" *ngIf="!isLoading && listDtata.length > 0; else emptyState">
      <div class="task-container">
        <ion-item 
          *ngFor="let task of listDtata; trackBy: trackByTaskCode"
          class="task-item"
          lines="none"
        >
          <ion-label class="task-info" *ngIf="isActiveTask(task.status)">
            <h4 class="task-name">{{task.taskName}}</h4>
            <p class="task-progress">完成率: {{task.completionRate}}</p>
          </ion-label>
          
          <ion-button
            slot="end"
            size="small"
            class="task-action-btn"
            [color]="task.status === '执行中' ? 'warning' : 'primary'"
            (click)="onStartClick(task)"
            *ngIf="isActiveTask(task.status)"
          >
            {{task.status === '未执行' ? '开始任务' : '继续任务'}}
          </ion-button>
        </ion-item>
      </div>

      <!-- 查看更多按钮 -->
      <button class="view-more-btn" (click)="goWork()">
        查看更多
      </button>
    </div>
  </main>

  <!-- 空状态模板 -->
  <ng-template #emptyState>
    <div class="empty-state" *ngIf="!isLoading">
      <img src="assets/menu/box2.png" alt="暂无数据" class="empty-icon" />
      <span class="empty-text">暂无数据</span>
    </div>
  </ng-template>
</ion-card-content>