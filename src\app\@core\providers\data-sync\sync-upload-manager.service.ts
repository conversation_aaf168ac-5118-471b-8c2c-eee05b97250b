import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { from } from 'rxjs';
import { concatMap, delay } from 'rxjs/operators';

import { StorageService } from '../storage.service';
import { UserInfoService } from '../user-Info.service';
import { SyncCacheManagerService } from './sync-cache-manager.service';
import { 
  SyncDataType, 
  SyncCacheItem, 
  UploadTask, 
  SyncDataTypeConfig 
} from './types/sync-data.types';

/**
 * 数据同步上传管理服务
 * 负责数据上传逻辑，包括批量上传、单个上传、网络请求处理等功能
 */
@Injectable({
  providedIn: 'root'
})
export class SyncUploadManagerService {

  /**
   * 是否正在上传，防止并发上传
   */
  private isUploading = false;

  /**
   * 批量上传任务之间的间隔时间（毫秒），用于削峰和提高稳定性
   */
  private readonly UPLOAD_INTERVAL = 500;

  constructor(
    private http: HttpClient,
    private storageService: StorageService,
    private userInfoService: UserInfoService,
    private cacheManager: SyncCacheManagerService
  ) { }

  /**
   * 检查是否正在上传
   */
  isCurrentlyUploading(): boolean {
    return this.isUploading;
  }

  /**
   * 尝试立即上传单个数据项
   */
  async tryImmediateUpload(type: SyncDataType, data: any, uploadUrl: string, method: string = 'POST'): Promise<{ success: boolean, code?: number, msg?: string }> {
    try {
      const payloadType = SyncDataTypeConfig[type]?.payloadType || 'array';
      const uploadPayload: any = payloadType === 'array' ? [data] : data;

      const response = await this.sendRequestByMethod(method, uploadUrl, uploadPayload);

      if (response && typeof response === 'object' && 'code' in response) {
        return { 
          success: response.code === 0, 
          code: response.code, 
          msg: response.msg 
        };
      } else {
        return { success: false };
      }
    } catch (err) {
      console.error('[SyncUploadManager] 立即上传失败:', err);
      return { success: false };
    }
  }

  /**
   * 批量上传所有业务类型的本地缓存数据
   * 采用 RxJS `concatMap` 保证任务按顺序、有间隔地执行
   */
  async uploadAllCachedData(): Promise<void> {
    console.log('[SyncUploadManager] 开始上传所有缓存数据...');
    if (this.isUploading) {
      console.log('[SyncUploadManager] 已有上传任务在进行中，本次跳过。');
      return;
    }
    this.isUploading = true;

    try {
      // 步骤一：从所有业务类型中收集所有需要上传的任务
      const allUploadTasks = await this.collectUploadTasks();

      if (!allUploadTasks.length) {
        console.log('[SyncUploadManager] 没有需要上传的缓存数据。');
        return;
      }

      console.log(`[SyncUploadManager] 共收集到 ${allUploadTasks.length} 个上传任务批次。`);

      // 步骤二：使用 RxJS 按顺序、有间隔地执行所有任务
      await from(allUploadTasks).pipe(
        // concatMap 会等待上一个内部 Observable 完成后，再开始下一个
        concatMap(task =>
          // from() 将 async/await 的 Promise 转为 Observable
          // pipe(delay) 在每个任务成功完成后，等待指定间隔时间
          from(this.processUploadTask(task)).pipe(delay(this.UPLOAD_INTERVAL))
        )
      ).toPromise(); // 等待整个流完成

      console.log('[SyncUploadManager] 所有缓存数据上传任务执行完毕。');

    } catch (error) {
      console.error('[SyncUploadManager] 执行上传任务流时发生意外错误', error);
    } finally {
      this.isUploading = false;
    }
  }

  /**
   * 收集所有需要上传的任务
   */
  private async collectUploadTasks(): Promise<UploadTask[]> {
    const types = Object.values(SyncDataType);
    const allUploadTasks: UploadTask[] = [];

    for (const type of types) {
      const cacheList = await this.cacheManager.getCache(type);
      if (!cacheList.length) continue;

      // 按上传URL对缓存项进行分组
      const urlMap = new Map<string, SyncCacheItem[]>();
      cacheList.forEach(item => {
        if (!urlMap.has(item.uploadUrl)) urlMap.set(item.uploadUrl, []);
        urlMap.get(item.uploadUrl)!.push(item);
      });

      for (const [url, items] of urlMap.entries()) {
        allUploadTasks.push({ type, url, items });
      }
    }

    return allUploadTasks;
  }

  /**
   * 处理单个上传任务（一个URL对应一批数据）
   * 包含了原有的完整上传、成功移除、失败增加重试次数的逻辑
   */
  private async processUploadTask(task: UploadTask): Promise<void> {
    const { type, url: uploadUrl, items } = task;
    const MAX_RETRY = 5; // 从配置中获取或作为参数传入

    const validItems = items.filter(i => i.retryCount < MAX_RETRY);
    console.log(`[SyncUploadManager] 处理任务: 类型=${type}, URL=${uploadUrl}, 有效项=${validItems.length}个`);
    if (!validItems.length) return;

    const method = (items[0]?.method || 'POST');
    const payloadType = SyncDataTypeConfig[type]?.payloadType || 'array';

    try {
      if (payloadType === 'array') {
        // 模式一：整个批次作为数组一次性上传
        const payload = validItems.map(i => i.data);
        await this.sendRequestByMethod(method, uploadUrl, payload);
        // 批量上传成功，移除所有已上传项
        const idsToRemove = validItems.map(item => item.id);
        await this.cacheManager.removeBatchFromCache(type, idsToRemove);
        console.log(`[SyncUploadManager] 批次任务成功 (类型=${type}, URL=${uploadUrl}), 已移除 ${validItems.length} 项。`);

      } else if (payloadType === 'object') {
        // 模式二：批次内的数据逐条上传
        const failedItems: SyncCacheItem[] = [];
        for (const item of validItems) {
          try {
            await this.sendRequestByMethod(method, uploadUrl, item.data);
            await this.cacheManager.removeFromCache(type, item.id);
          } catch (err) {
            item.retryCount++; // 单条失败，增加重试次数
            failedItems.push(item);
            console.error(`[SyncUploadManager] 单条上传异常 (ID=${item.id})`, item, err);
          }
        }
        // 如果有失败的项，统一更新它们的重试次数
        if (failedItems.length > 0) {
          await this.cacheManager.updateCacheItems(type, failedItems);
        }
      }
    } catch (err) { // 这个 catch 主要捕获 'array' 模式下的批量上传失败
      console.error(`[SyncUploadManager] 批量上传失败 (类型=${type}, URL=${uploadUrl})`, err);
      // 批量上传失败，为批次内所有项增加重试次数
      validItems.forEach(item => item.retryCount++);
      await this.cacheManager.updateCacheItems(type, validItems);
    }

    // 检查并警告那些达到最大重试次数的项
    items.filter(i => i.retryCount >= MAX_RETRY).forEach(i => {
      console.warn(`[SyncUploadManager] 数据项已达最大重试次数，不再上传 (ID=${i.id})`, i);
    });
  }

  /**
   * 根据method动态分发请求，并在header中添加token
   */
  private async sendRequestByMethod(method: string, url: string, data: any): Promise<any> {
    let token = this.userInfoService.token;
    if (!token) {
      const storedToken = await this.storageService.get('token').toPromise();
      if (storedToken) {
        try {
          token = atob(storedToken);
        } catch (e) {
          token = storedToken;
        }
      }
    }

    const m = (method || 'POST').toUpperCase();
    const headers = token ? { headers: { token } } : {};
    console.log('[SyncUploadManager] 发起请求:', { method: m, url, data, headers: headers });

    try {
      let response;
      if (m === 'GET') {
        response = await this.http.get(url, { ...headers, params: data }).toPromise();
      } else if (m === 'PUT') {
        response = await this.http.put(url, data, headers).toPromise();
      } else {
        response = await this.http.post(url, data, headers).toPromise();
      }
      console.log('[SyncUploadManager] 收到响应:', response);
      return response;
    } catch (err) {
      console.error('[SyncUploadManager] 请求异常:', err);
      throw err; // 向上抛出异常，让调用方(processUploadTask)能捕获到
    }
  }
}
