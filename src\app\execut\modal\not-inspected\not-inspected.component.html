<!-- 关键点打卡页面 -->
<ion-content [style.height]="isViewMode ? '100%' : 'calc(100% - 56px)'">
  <!-- 关键点基础信息展示区 -->
  <div class="keypoint-info-list" *ngIf="currentKeyPoint">
    <div class="keypoint-info-row">
      <span class="keypoint-label point">🏢 部门：</span>
      <span class="keypoint-value">{{ currentKeyPoint?.depName }}</span>
    </div>
    <div class="keypoint-info-row">
      <span class="keypoint-label point">🚩 桩号：</span>
      <span class="keypoint-value">{{ currentKeyPoint?.stakeName }}</span>
    </div>
    <div class="keypoint-info-row">
      <span class="keypoint-label point">📍 关键点：</span>
      <span class="keypoint-value">{{ currentKeyPoint?.pointName }}</span>
    </div>
    <div class="keypoint-info-row">
      <span class="keypoint-label coord">🧭 坐标：</span>
      <span class="keypoint-value">{{ formattedPoint }}</span>
    </div>
  </div>

  <ion-item-divider>
    <ion-label class="required-label">
      未巡检原因
      <span class="required-star" *ngIf="!isViewMode">*</span>
    </ion-label>
    <ion-textarea
      [(ngModel)]="reasonText"
      [placeholder]="isViewMode ? '' : '请输入未巡检原因'"
      [readonly]="isViewMode"
      rows="3"
    >
    </ion-textarea>
  </ion-item-divider>

  <ion-item lines="none">
    <ion-label>雨天不巡查</ion-label>
    <ion-toggle slot="end" [(ngModel)]="isItRaining" [disabled]="isViewMode"></ion-toggle>
  </ion-item>
</ion-content>

<!-- 底部统一提交按钮 -->
<ion-footer *ngIf="showSubmitButton">
  <ion-toolbar>
    <ion-button expand="block" color="primary"
      (click)="onSubmit()" [disabled]="!this.reasonText || this.reasonText.trim() === ''">
      提交
    </ion-button>
  </ion-toolbar>
</ion-footer>