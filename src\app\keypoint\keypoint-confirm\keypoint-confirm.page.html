<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>关键点确认</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="refresh()">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <!-- 地图区域 (上半部分) -->
  <div class="map-container">
    <app-confirm-map-view 
      [points]="unconfirmedPoints">
    </app-confirm-map-view>
  </div>
  
  <!-- 列表区域 (下半部分) -->
  <div class="list-container">
    <!-- 统计信息 -->
    <div class="count-info">
      <div class="count-item">
        <ion-icon name="location" color="primary"></ion-icon>
        <span>未确认关键点: <strong>{{unconfirmedCount}}</strong> 个</span>
      </div>
    </div>
    

    
    <!-- 关键点列表 -->
    <div class="points-list">
      <ion-refresher slot="fixed" (ionRefresh)="refresh($event)">
        <ion-refresher-content></ion-refresher-content>
      </ion-refresher>
      
      <div *ngIf="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>加载中...</p>
      </div>
      
      <app-confirm-list-view 
        [points]="unconfirmedPoints"
        (confirmSingle)="confirmSingle($event.point, $event.isOk)"
        (pointLocate)="onPointLocate($event)">
      </app-confirm-list-view>
    </div>
  </div>
</ion-content>