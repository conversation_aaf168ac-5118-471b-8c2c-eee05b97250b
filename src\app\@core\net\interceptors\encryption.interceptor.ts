import { Injectable } from '@angular/core';
import { <PERSON>ttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { AesEncryptionService } from '../aes-encryption.service';
import { EncryptionDebugLoggerService } from '../encryption-debug-logger.service';


@Injectable()
export class EncryptionInterceptor implements HttpInterceptor {
  // 白名单(不需要加密的接口)
  private readonly encryptionWhitelist = new Set<string>([]);

  constructor(
    private aesEncryptionService: AesEncryptionService,
    private encryptionDebugLogger: EncryptionDebugLoggerService
  ) {}

  private isEncryptionEnabled(): boolean {
    return environment.encryption?.enabled ?? false;
  }

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    const isMultipart = req.headers.get('Content-Type')?.includes('multipart/form-data');
    const isInWhitelist = Array.from(this.encryptionWhitelist).some(url => req.url.includes(url));

    if (!this.isEncryptionEnabled() || isInWhitelist || isMultipart) {
      return next.handle(req);
    }
    
    const encryptedReq = this.encryptRequest(req);

    return next.handle(encryptedReq).pipe(
      map(event => {
        if (event instanceof HttpResponse && event.body?.cip) {
           return this.decryptResponse(encryptedReq.url, event);
        }
        return event;
      })
    );
  }

  private encryptRequest(req: HttpRequest<any>): HttpRequest<any> {
    const urlObj = new URL(req.urlWithParams, 'http://dummy');
    
    if (req.method === 'GET') {
      const paramsObj: any = {};
      urlObj.searchParams.forEach((value, key) => { paramsObj[key] = value; });
      this.encryptionDebugLogger.logOriginalRequest(req.url, paramsObj);
      const cip = this.aesEncryptionService.encryptData(paramsObj);
      const baseUrl = req.url.split('?')[0];
      const encryptedUrl = `${baseUrl}?cip=${cip}`;
      this.encryptionDebugLogger.logEncryptedRequest(baseUrl, encryptedUrl);
      return req.clone({ url: encryptedUrl, params: new HttpParams() });
    } 
    
    if (["POST", "PUT", "DELETE"].includes(req.method)) {
      const messageReplayTime = urlObj.searchParams.get('messageReplayTime') || '';
      const cipUrl = this.aesEncryptionService.encryptData({ messageReplayTime });
      const baseUrl = req.url.split('?')[0];
      const encryptedUrl = `${baseUrl}?cip=${cipUrl}`;
      
      const bodyObj = req.body && typeof req.body === 'object' ? { ...req.body } : {};
      this.encryptionDebugLogger.logOriginalRequest(req.url, undefined, bodyObj);
      const cipBody = this.aesEncryptionService.encryptData(bodyObj);
      const encryptedBody = { cip: cipBody };
      this.encryptionDebugLogger.logEncryptedRequest(baseUrl, undefined, encryptedBody);
      return req.clone({ url: encryptedUrl, body: encryptedBody, params: new HttpParams() });
    }

    return req; // For other methods, do not encrypt
  }
  
  private decryptResponse(requestUrl: string, event: HttpResponse<any>): HttpResponse<any> {
    try {
      const decrypted = this.aesEncryptionService.decryptData(event.body.cip);
      this.encryptionDebugLogger.logDecryptedResponse(requestUrl, decrypted);
      return event.clone({ body: decrypted });
    } catch (e) {
      console.error('响应解密失败:', e, '请求URL:', requestUrl);
      // 返回原始事件，让后续处理器决定如何处理这个错误
      return event;
    }
  }
}