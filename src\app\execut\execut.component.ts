import { Component, HostListener, Inject, Input, <PERSON>Z<PERSON>, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { ModalController, Platform } from '@ionic/angular';
import { Subject, Subscription, BehaviorSubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CurrentTime, DetailsMode } from '../@core/base/environment';
import { ToastService } from '../@core/providers/toast.service';
import { KeyPointsComponent } from '../share/map-component/key-points/key-points.component';
import { LocationSelectComponent } from '../share/map-component/location-select/location-select.component';
import { MapComponent } from '../share/map-component/map/map.component';
import { KeyPoint, KeyPointService } from '../share/map-component/service';
import { Task } from '../work/class/work';
import { CameraListComponent } from './modal/camera-list/camera-list.component';
import { ControlBarComponent } from './control-bar/control-bar.component';
import { EvreportComponent } from './modal/evreport/evreport.component';
import { KeyPointManagerService, LocationService, ModalManagerService, WeatherStateService } from './services';

@Component({
  selector: 'ost-execut',
  templateUrl: './execut.component.html',
  styleUrls: ['./execut.component.scss']
})
export class ExecutComponent implements OnInit, OnDestroy {
  // 地图组件
  @ViewChild('ostMap', { static: false }) ostMap: MapComponent;
  // 任务栏
  @ViewChild('trackBar', { static: false }) ctrlBar: ControlBarComponent;
  // 模式  create: 新任务  continue: 继续任务
  @Input() executState: ExecutState = 'create';
  // 中心点
  @Input() centerPoint: number[];
  // 任务
  @Input() task: Task;
  @Input() isModal = false;
  // 地图点击事件
  @Input() mapClick: boolean;
  // 需要操作的业务图层ID
  layerIds = ['p_pipe_joint_info', 'df_camera'];
  // 关键点列表
  keyPoints: KeyPoint[] = [];
  // 响应式关键点数据和加载状态
  loading$ = new BehaviorSubject<boolean>(false);
  keyPoints$ = new BehaviorSubject<KeyPoint[]>([]);
  // 加载状态
  loading: boolean = false;
  // 打卡按钮显示状态
  canPunchIn: boolean = false;
  // 当前关键点
  currentKeyPoint: any = null;
  // 当前坐标
  coordinate: number[];

  // 订阅
  private weatherSubscription: Subscription;
  private destroy$ = new Subject<void>();

  constructor(
    public modalCtrl: ModalController, public platform: Platform,
    @Inject(CurrentTime) public currentTime: string,
    private locationService: LocationService,
    private modalManagerService: ModalManagerService,
    private keyPointManagerService: KeyPointManagerService,
    private weatherStateService: WeatherStateService,
    private keyPointService: KeyPointService,
    private toastSer: ToastService,
    private ngZone: NgZone
  ) {
    // 初始化时间
    this.currentTime = String(Date.now());
  }

  async ngOnInit(): Promise<void> {
    // 获取关键点数据
    this.getkeyPoints();

    if (this.executState === 'continue') {
      this.currentTime = this.task.groupCode;
    }

    // 初始化定位配置
    const taskCode = this.task ? this.task.taskCode : '';
    await this.locationService.initLocationConfigure(taskCode, this.currentTime);

    // 开始位置监听（但不启动后台定位服务，等待用户点击开始按钮）
    this.locationService.startLocationTracking(this.onLocationChange);

    // 订阅天气状态变化
    this.weatherSubscription = this.weatherStateService.getWeatherState().subscribe(weatherState => {
      this.task.isItRaining = weatherState.isItRaining;
    });
  }

  /**
   * 获取关键点
   */
  getkeyPoints() {
    this.loading$.next(true);
    this.keyPointService.getKeyPointsByTaskCode(this.task.taskCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe(result => {
        const { code, data, msg } = result;
        if (code === 0) {
          this.keyPoints = data || [];
          this.keyPoints$.next(this.keyPoints);
          // 初始化关键点
          this.keyPointManagerService.initKeyPoints(this.keyPoints);
          // 新增：渲染关键点及范围
          if (this.ostMap && this.ostMap.setKeyPoints) {
            this.ostMap.setKeyPoints(this.keyPoints);
          }
        } else {
          this.toastSer.presentToast(msg, 'danger');
        }
        this.loading$.next(false);
      }, () => {
        this.loading$.next(false);
      });
  }

  /**
   * 是否展示天气开关
   */
  get showWeatherSwitch(): boolean {
    return this.weatherStateService.shouldShowWeatherSwitch(this.task.inspectionMethod, this.executState);
  }

  /**
   * 获取天气状态
   */
  get isSunny(): boolean {
    return this.weatherStateService.isSunny;
  }

  /**
   * 获取关键点图层
   */
  get KeyPointLayer() {
    return this.keyPointManagerService.getKeyPointLayer();
  }

  /**
   * 获取模态框状态
   */
  get modalStates() {
    return this.modalManagerService.getModalStates();
  }

  /**
   * 切换天气状态
   */
  onToggleChange(event: any) {
    this.weatherStateService.toggleWeather();
  }

  /**
   * 事件上报 新增
   * @param coordinate 坐标信息 
   */
  async onEvReportClick(coordinate): Promise<void> {
    await this.modalManagerService.createModalWithGuard('evReport', {
      component: EvreportComponent,
      componentProps: {
        coordinate,
        keyPoints: this.keyPoints,
        isModal: this.isModal,
        modelMode: DetailsMode.ADD
      },
    }, (data, role) => {
      if (role === 'confirm') {
        // this.ostMap.refreshBusinessLayer('inspect_event', data.eventGeom);
      }
    });
  }

  /**
   * 关键点 新增
   * @param coordinate 坐标信息
   */
  async onKeyPointClick(coordinate): Promise<void> {
    await this.modalManagerService.createModalWithGuard('keyPoint', {
      component: LocationSelectComponent,
      componentProps: {
        coordinate,
        taskCode: this.task.taskCode,
        modelMode: DetailsMode.ADD,
      },
      cssClass: 'app-location-select',
    }, (data, role) => {
      if (role === 'confirm') {
        this.ostMap.refreshBusinessLayer('inspect_point', data.centerCoord);
      }
    });
  }

  /**
   * 位置变化回调
   */
  onLocationChange = (location: BackgroundGeolocationResponse) => {
    // 使用 NgZone.run 确保在后台状态下也能正常执行
    this.ngZone.run(() => {
      // 获取当前位置
      const currentCoord = this.locationService.getCurrentCoordinate(location);
      this.coordinate = currentCoord;

      // 更新关键点（这里会触发关键点提醒服务）
      this.keyPointManagerService.updateLocation(currentCoord);

      // 使用服务方法判断是否在范围内，并获取当前关键点
      const { inRange, keyPoint } = this.keyPointManagerService.isInRange(currentCoord);

      // 检查是否移出了关键点范围且打卡页面已打开
      // if (!inRange && this.modalManagerService.isModalOpen('punchIn')) {
      //   // 自动关闭打卡页面
      //   this.modalManagerService.closeModal('punchIn');
      // }

      this.canPunchIn = inRange;
      this.currentKeyPoint = inRange ? keyPoint : null;

      // 新增：到达关键点时前端变色，并同步更新keyPoints数组
      if (inRange && keyPoint) {
        // 1. 同步更新keyPoints数组
        const idx = this.keyPoints.findIndex(p => p.id === keyPoint.id);
        if (idx !== -1) {
          this.keyPoints[idx].state = '已巡';
        }

        // 2. 地图变色（如果地图可用）
        if (this.ostMap && this.ostMap.updateKeyPointStatus) {
          this.ostMap.updateKeyPointStatus(keyPoint.id, '已巡');
        }

        // 3. 自动打卡并缓存/批量上传，根据结果决定是否刷新关键点数据
        this.keyPointService.clockInWithCacheBatch(
          this.task.taskCode,
          keyPoint.pointCode,
          this.coordinate[0],
          this.coordinate[1]
        ).then(result => {
          // 如果网络通畅且自动打卡成功，重新获取关键点数据
          if (result.shouldRefreshKeyPoints) {
            this.getkeyPoints();
          }
        }).catch(error => {
          console.error('自动打卡失败:', error);
        });
      }
    });
  };

  /**
   * 视频列表
   */
  async onVideoClick() {
    await this.modalManagerService.createModalWithGuard('video', {
      component: CameraListComponent,
      cssClass: 'camera-list-modal',
    });
  }

  /**
   * 关键点列表
   */
  async seePoints() {
    await this.modalManagerService.createModalWithGuard('points', {
      component: KeyPointsComponent,
      componentProps: {
        disabledClick: false,
        taskCode: this.task.taskCode,
        loading$: this.loading$,
        keyPoints$: this.keyPoints$,
        showDataStatus: false, // 在执行页面不显示数据状态列
        currentCoordinate: this.coordinate, // 传递当前实时坐标
      },
      cssClass: 'camera-list-modal'
    }, (data, role) => {
      // 处理关键点列表模态框关闭后的回调
      if (data && data.shouldRefreshKeyPoints) {
        // 网络通畅且提交成功时，重新获取关键点数据
        this.getkeyPoints();
      }
    });
  }

  @HostListener('document:ionBackButton', ['$event'])
  async onCloseClick($event: CustomEvent): Promise<void> {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      $event.stopPropagation();
      $event.preventDefault();
      if (this.ctrlBar.trackRunState !== 'running' &&
        this.ctrlBar.trackRunState !== 'pause') {
        await this.modalCtrl.dismiss();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    // 清理定位服务
    this.locationService.destroy();

    // 清理关键点服务
    this.keyPointManagerService.destroy();

    // 清理天气订阅
    if (this.weatherSubscription) {
      this.weatherSubscription.unsubscribe();
    }

    // 重置模态框状态
    this.modalManagerService.resetModalStates();
  }
}

/**
 * 执行状态
 */
export declare type ExecutState = 'create' | 'continue';