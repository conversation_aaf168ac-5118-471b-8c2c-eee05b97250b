import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { PageGridResult, RequestResult } from '../../@core/base/request-result';

/**
 * 关键点查看页面数据模型
 */
export interface KeyPointViewData {
  pointCode: string;
  bufferRange: number;
  pointName: string;
  geom: string;
  stakeName: string;
  depName: string;
  isItRaining: string;
  depCode: string;
  stakeCode: string;
  inspectionMethod: string;
}

/**
 * 关键点查询参数
 */
export interface KeyPointQueryParams {
  depCode?: string;
  pointName?: string;
  inspectionMethod?: string; // 巡视|巡查
  isItRaining?: string; // 是|否
  pageSize?: number;
  pageNum?: number;
}

/**
 * 关键点修改参数
 */
export interface UpdateKeyPointParams {
  pointGeom: string;
  bufferRange: number;
  pointCode: string;
  pointName: string;
  isItRaining: string;
  inspectionMethod: string;
}

/**
 * 关键点删除参数 - 直接传递编码数组
 */
export type DeleteKeyPointParams = string[];

/**
 * 关键点查看服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root'
})
export class KeypointViewService extends Resource {

  constructor() {
    super();
  }

  /**
   * 分页查看巡检关键点信息
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/msg/grid',
    method: ResourceRequestMethod.Get
  })
  getKeyPointsGrid!: IResourceMethodObservable<KeyPointQueryParams, PageGridResult<KeyPointViewData[]>>;

  /**
   * 查看巡检关键点信息列表(不分页)
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/msg/list',
    method: ResourceRequestMethod.Get
  })
  getKeyPointsList!: IResourceMethodObservable<KeyPointQueryParams, RequestResult<KeyPointViewData[]>>;

  /**
   * 修改关键点信息
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/msg',
    method: ResourceRequestMethod.Put
  })
  updateKeyPoint!: IResourceMethodObservable<UpdateKeyPointParams, RequestResult<any>>;

  /**
   * 删除关键点
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/msg',
    method: ResourceRequestMethod.Delete
  })
  deleteKeyPoints!: IResourceMethodObservable<DeleteKeyPointParams, RequestResult<any>>;
}