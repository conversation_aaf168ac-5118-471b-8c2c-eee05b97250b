import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { AlarmComponent } from './alarm.component';
import { ShareModule } from "../share/share.module";
import { IonicModule } from '@ionic/angular';
import { AlarmListComponent } from './alarm-list/alarm-list.component';
import { FormsModule } from '@angular/forms';

const routes: Routes = [
  {
    path: '',
    component: AlarmComponent
  }
];

const COMP = [
  AlarmComponent,
  AlarmListComponent
];

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    RouterModule.forChild(routes),
    ShareModule
  ],
  declarations: [...COMP]
})
export class AlarmPageModule { }
