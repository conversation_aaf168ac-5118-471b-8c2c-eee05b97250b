import { Component, Input, OnInit } from '@angular/core';
import { OstTreeListItem } from 'src/app/share/ost-tree-list/ost-tree-list.service';
import { InputSearchSourceService } from '../../input-search-source.service';

@Component({
  selector: 'search-source-stake',
  templateUrl: './search-source-stake.component.html',
  styleUrls: ['./search-source-stake.component.scss']
})
export class SearchSourceStakeComponent implements OnInit {
  // 管线ID
  @Input() pipelineId: string;
  // 请求地址
  @Input() interfaceUrl: string = '/work-basic/api/v2/basic/stake/list';
  // 是否分页
  @Input() isPage: boolean = false;
  // 标签项名称
  @Input() labelName: string = 'stakeName';
  // 标签项值
  @Input() labelValue: string = 'stakeCode';
  constructor(public searchSer: InputSearchSourceService) { }

  ngOnInit(): void { }

  onToggleSubMenu(item: OstTreeListItem): void {
    this.searchSer.change(item.data[this.labelName], item.data[this.labelValue]);
  }
  onItemClick(item: OstTreeListItem): void {
    this.searchSer.change(item.data[this.labelName], item.data[this.labelValue]);
  }

}
