.map-view-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--ion-color-light);
}

.confirm-map {
  width: 100%;
  height: 100%;
  display: block;
}

.map-controls {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
  
  .control-btn {
    width: 40px;
    height: 40px;
    margin: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    
    &:hover {
      background: white;
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    &:active {
      transform: scale(0.95);
    }
    
    &[disabled] {
      opacity: 0.5;
      pointer-events: none;
    }
    
    ion-icon {
      font-size: 20px;
      color: var(--ion-color-primary);
    }
  }
}

.map-legend {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 12px;
    color: var(--ion-color-dark);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .legend-icon {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      flex-shrink: 0;
      
      &.normal-point {
        background: var(--ion-color-primary);
        border: 2px solid white;
        box-shadow: 0 0 0 1px var(--ion-color-primary);
      }
      
      &.selected-point {
        background: var(--ion-color-success);
        border: 2px solid white;
        box-shadow: 0 0 0 1px var(--ion-color-success);
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 4px;
          height: 4px;
          background: white;
          border-radius: 50%;
        }
      }
      
      &.rain-point {
        background: var(--ion-color-warning);
        border: 2px solid white;
        box-shadow: 0 0 0 1px var(--ion-color-warning);
      }
    }
    
    span {
      font-weight: 500;
    }
  }
}

.map-stats {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  gap: 12px;
  z-index: 1000;
  
  .stats-item {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 6px 10px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-size: 13px;
    font-weight: 600;
    color: var(--ion-color-dark);
    
    ion-icon {
      font-size: 16px;
    }
  }
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2000;
  
  ion-spinner {
    margin-bottom: 16px;
    --color: var(--ion-color-primary);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

.map-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 248, 248, 0.95);
  z-index: 1500;
  
  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--ion-color-medium);
  }
  
  p {
    margin: 0;
    font-size: 16px;
    color: var(--ion-color-medium);
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .map-controls {
    top: 8px;
    right: 8px;
    
    .control-btn {
      width: 36px;
      height: 36px;
      
      ion-icon {
        font-size: 18px;
      }
    }
  }
  
  .map-legend {
    bottom: 8px;
    left: 8px;
    padding: 10px;
    min-width: 120px;
    
    .legend-item {
      font-size: 11px;
      margin-bottom: 4px;
      
      .legend-icon {
        width: 10px;
        height: 10px;
      }
    }
  }
  
  .map-stats {
    top: 8px;
    left: 8px;
    
    .stats-item {
      padding: 4px 8px;
      font-size: 12px;
      
      ion-icon {
        font-size: 14px;
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.map-legend,
.map-stats {
  animation: fadeIn 0.3s ease-out;
}

// 悬浮效果
.map-legend:hover,
.map-stats .stats-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}