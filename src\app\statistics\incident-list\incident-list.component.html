<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div color="primary" class="search-bar">
      <div class="search-bar-parent">
        <ion-icon class="login-form-input-icon" name="search-outline"></ion-icon>
        <ion-input clearInput
          placeholder="请输入事件名称"
          [(ngModel)]="eventParams.eventName"
          (ngModelChange)="onClearInput()">
        </ion-input>
      </div>
      <ion-note slot="end" class="title-end-operate" style="padding-right:14px;" (click)="onSearch()">
        搜索
      </ion-note>
    </div>
  </ion-toolbar>
</ion-header>

<div class="segment-container">
  <ion-segment [scrollable]="true" [value]="activeTabId" (ionChange)="onSegmentChange($event)">
    <ion-segment-button value="管道占压">
      管道占压
    </ion-segment-button>
    <ion-segment-button value="设备失效">
      设备失效
    </ion-segment-button>
    <ion-segment-button value="第三方施工">
      第三方施工
    </ion-segment-button>
    <ion-segment-button value="隐患上报">
      隐患上报
    </ion-segment-button>
  </ion-segment>
</div>

<ion-content>
  <!-- 下拉刷新 -->
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ng-container *ngIf="gridData.length > 0 ;else noData">
    <div *ngFor="let item of gridData" class="incident-card" (click)="getDetailInfo(item)">
      <!-- 卡片头部 -->
      <div class="card-header">
        <div class="event-type-badge" [ngClass]="getEventTypeClass(item.eventType)">
          {{item.eventType}}
        </div>
        <div class="event-status" [ngClass]="getStatusClass(item.eventStatus)">
          {{item.eventStatus}}
        </div>
      </div>

      <!-- 事件描述 -->
      <div class="event-description">
        <ion-icon name="document-text-outline" class="desc-icon"></ion-icon>
        <span class="desc-text">{{item.eventDesc || '暂无描述'}}</span>
      </div>

      <!-- 管线信息 -->
      <div class="pipeline-info">
        <ion-icon name="git-branch-outline" class="pipeline-icon"></ion-icon>
        <span class="pipeline-text">{{item.pipelineName || '未指定管线'}}</span>
      </div>

      <!-- 详细信息 -->
      <div class="detail-info">
        <div class="info-row">
          <div class="info-item">
            <ion-icon name="business-outline" class="info-icon"></ion-icon>
            <span class="info-label">部门：</span>
            <span class="info-value">{{item.depName}}</span>
          </div>
          <div class="info-item">
            <ion-icon name="person-outline" class="info-icon"></ion-icon>
            <span class="info-label">上报人：</span>
            <span class="info-value">{{item.reportUserName}}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <ion-icon name="time-outline" class="info-icon"></ion-icon>
            <span class="info-label">上报时间：</span>
            <span class="info-value">{{formatTime(item.reportingTime)}}</span>
          </div>
        </div>

        <!-- 处理信息（如果有） -->
        <div class="info-row" *ngIf="item.processTime || item.processMeasure">
          <div class="info-item" *ngIf="item.processTime">
            <ion-icon name="checkmark-circle-outline" class="info-icon"></ion-icon>
            <span class="info-label">处理时间：</span>
            <span class="info-value">{{item.processTime | date:'MM-dd HH:mm'}}</span>
          </div>
        </div>

        <div class="process-measure" *ngIf="item.processMeasure">
          <ion-icon name="clipboard-outline" class="info-icon"></ion-icon>
          <span class="info-label">处理措施：</span>
          <span class="measure-text">{{item.processMeasure}}</span>
        </div>
      </div>

      <!-- 图片指示器 -->
      <div class="image-indicator" *ngIf="item.picCode && item.picCode.length > 0">
        <ion-icon name="images-outline"></ion-icon>
        <span>{{item.picCode.length}}张图片</span>
      </div>
    </div>
  </ng-container>

  <!-- 上拉加载更多 -->
  <ion-infinite-scroll threshold="50px" style="margin-top: 20px;" (ionInfinite)="loadData($event)">
    <ion-infinite-scroll-content 
      [loadingSpinner]="spinnerType" 
      [loadingText]="isShowNoMoreStr"
    >
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>

<!-- 暂无数据 -->
<ng-template #noData>
  <div class="no-data">
    <img src="assets/menu/box2.png" />
    <span class="no-data-span">暂无{{activeTabId}}事件</span>
    <p class="no-data-tip">可以尝试切换其他事件类型查看</p>
  </div>
</ng-template>