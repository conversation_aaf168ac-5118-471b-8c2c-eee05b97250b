ion-content {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
}

.map-container {
  height: 50vh;
  width: 100%;
  position: relative;
  border-bottom: 1px solid var(--ion-color-light);
}

.list-container {
  height: 50vh;
  display: flex;
  flex-direction: column;
  background: var(--ion-color-light-tint);
}

.count-info {
  padding: 12px 16px;
  background: var(--ion-color-primary-tint);
  color: var(--ion-color-primary-contrast);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--ion-color-primary-shade);
  
  .count-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    
    ion-icon {
      font-size: 16px;
    }
    
    strong {
      color: var(--ion-color-warning);
    }
  }
}

.batch-actions {
  padding: 12px 16px;
  background: white;
  display: flex;
  gap: 12px;
  border-bottom: 1px solid var(--ion-color-light);
  
  ion-button {
    flex: 1;
    margin: 0;
    
    &[disabled] {
      opacity: 0.5;
    }
  }
}

.points-list {
  flex: 1;
  overflow-y: auto;
  background: white;
  
  ion-list {
    padding: 0;
  }
  
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 80px;
    border-bottom: 1px solid var(--ion-color-light);
    transition: background-color 0.2s ease;
    
    &.selected {
      --background: var(--ion-color-primary-tint);
      --color: var(--ion-color-primary-contrast);
    }
    
    &:hover {
      --background: var(--ion-color-light-tint);
    }
    
    ion-label {
      margin: 8px 0;
      
      h2 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
        color: var(--ion-color-dark);
      }
      
      p {
        font-size: 13px;
        color: var(--ion-color-medium);
        margin: 2px 0;
        display: flex;
        align-items: center;
        gap: 4px;
        
        ion-icon {
          font-size: 14px;
          min-width: 14px;
        }
      }
    }
  }
}

.rain-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background: var(--ion-color-warning-tint);
  color: var(--ion-color-warning-contrast);
  border-radius: 12px;
  font-size: 11px;
  display: inline-flex;
  align-items: center;
  gap: 2px;
  
  ion-icon {
    font-size: 12px;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  ion-button {
    margin: 0;
    width: 36px;
    height: 36px;
    
    ion-icon {
      font-size: 18px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);
  
  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--ion-color-success);
  }
  
  p {
    margin: 0;
    font-size: 16px;
    text-align: center;
  }
}

// 响应式设计
@media (max-height: 600px) {
  .map-container {
    height: 40vh;
  }
  
  .list-container {
    height: 60vh;
  }
}

@media (min-height: 800px) {
  .map-container {
    height: 55vh;
  }
  
  .list-container {
    height: 45vh;
  }
}

// 选中状态动画
@keyframes selectAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

ion-item.selected {
  animation: selectAnimation 0.3s ease;
}

// 按钮点击效果
ion-button {
  transition: transform 0.1s ease;
  
  &:active {
    transform: scale(0.95);
  }
}