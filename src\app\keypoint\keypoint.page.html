<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button (click)="goBack()" defaultHref="/tabs/home"></ion-back-button>
    </ion-buttons>
    <ion-title>关键点管理</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">关键点管理</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="container">
    <ion-card>
      <ion-card-header>
        <ion-card-title>关键点管理</ion-card-title>
        <ion-card-subtitle>管理和查看所有关键点信息</ion-card-subtitle>
      </ion-card-header>
      
      <ion-card-content>
        <!-- 关键点查看功能入口 -->
        <ion-button 
          expand="block" 
          fill="solid" 
          color="primary"
          (click)="goToKeyPointView()"
          class="view-button">
          <ion-icon name="map-outline" slot="start"></ion-icon>
          关键点查看
          <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
        </ion-button>
        
        <ion-text color="medium" class="description">
          <p>支持地图和列表两种查看方式，可按巡检类型、天气条件等进行筛选</p>
        </ion-text>
        
        <!-- 分隔线 -->
        <hr class="divider">
        
        <!-- 其他功能 -->
        <ion-list>
          <ion-item button>
            <ion-icon name="add-circle-outline" slot="start" color="success"></ion-icon>
            <ion-label>
              <h2>添加关键点</h2>
              <p>新增巡检关键点</p>
            </ion-label>
            <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
          </ion-item>
          
          <ion-item button>
            <ion-icon name="settings-outline" slot="start" color="warning"></ion-icon>
            <ion-label>
              <h2>关键点设置</h2>
              <p>配置关键点参数</p>
            </ion-label>
            <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
          </ion-item>
          
          <ion-item button>
            <ion-icon name="analytics-outline" slot="start" color="tertiary"></ion-icon>
            <ion-label>
              <h2>巡检统计</h2>
              <p>查看巡检数据统计</p>
            </ion-label>
            <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
    
    <ion-fab vertical="bottom" horizontal="end" slot="fixed">
      <ion-fab-button>
        <ion-icon name="add"></ion-icon>
      </ion-fab-button>
    </ion-fab>
  </div>
</ion-content>