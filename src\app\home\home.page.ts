import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>roller, ModalController } from '@ionic/angular';
import { ShareMethodService } from '../@core/providers/share-method.service';
import { UserInfoService } from '../@core/providers/user-Info.service';
import { NetworkService } from '../@core/providers/network.service';
import { ChangPasswordComponent } from '../self/chang-password/chang-password.component';
import { PageEventService } from './home.event';
import { DataSyncManagerService } from '../@core/providers/data-sync';
import { ToastService } from '../@core/providers/toast.service';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss']
})

export class HomePage implements OnInit, OnDestroy {
  // 下拉刷新事件
  refreshEvent: any;
  // 定时刷新定时器
  private refreshTimer: any;
  constructor(
    public userInfo: UserInfoService, private alertCtrl: AlertController,
    private modalCtrl: ModalController, private toastService: ToastService,
    private dataEvent: PageEventService, private shareUtils: ShareMethodService,
    private dataSyncManager: DataSyncManagerService,
    private networkService: NetworkService
  ) {
  }
  ngOnInit(): void {
    this.checkPassword();

    if (this.networkService.isOnline()) {
      // 每分钟定时刷新
      this.refreshTimer = setInterval(() => this.dataEvent.sendData('home', true), 60 * 1000);
    }
  }

  ngOnDestroy(): void {
    // 清理定时器，防止内存泄漏
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * 下拉刷新
   */
  doRefresh(event): void {
    this.dataEvent.sendData('home', true);
    this.refreshEvent = event;
    setTimeout(() => {
      this.closeRefresh();
    }, 1000);
  }

  // 关闭刷新
  closeRefresh(): void {
    if (this.refreshEvent) {
      this.refreshEvent.target.complete();
    }
  }

  /**
   * 密码到期提醒
   */
  checkPassword() {
    if (this.userInfo.expireTime) {
      const date = this.shareUtils.daysUntilDeadline(this.userInfo.expireTime);
      if (date <= 5) {
        this.openDialog(date);
      }
    }
  }

  async openDialog(date: number): Promise<void> {
    const msg = date <= 0 ? `您的密码已到期，请及时修改密码` : `您的密码还有${date}天到期，请及时修改密码`;
    const results = await this.alertCtrl.create({
      header: '密码到期提醒!',
      message: msg,
      buttons: [
        {
          text: '取消',
          role: 'cancel',
          cssClass: 'secondary',
        }, {
          text: '修改密码',
          cssClass: 'Okay',
          handler: () => {
            this.changePassword();
          }
        }
      ]
    });
    await results.present();
  }

  /**
   * 修改密码
   */
  async changePassword(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ChangPasswordComponent,
      backdropDismiss: false
    });
    return await modal.present();
  }

  /**
   * 手动上传缓存数据
   */
  async onManualUpload(): Promise<void> {
    try {
      await this.dataSyncManager.uploadAllCachedData();
      this.toastService.presentToast('手动上传已触发，请查看日志', 'success');
    } catch (err) {
      this.toastService.presentToast('手动上传失败', 'danger');
    }
  }

}
