<ng-container *ngIf="formGroup">
  <ng-container *ngIf="ostFormControlNames">
    <ng-container *ngIf="errorCode">
      <span
        class="error ion-padding"
        *ngIf="
          formGroup.get(ostFormControlNames).hasError(errorCode) &&
          formGroup.get(ostFormControlNames).touched
        "
      >
        <ng-content></ng-content>
      </span>
    </ng-container>
  </ng-container>
</ng-container>
