import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Coordinates, Geolocation } from '@ionic-native/geolocation/ngx';
import { BackgroundGeolocation } from '@ionic-native/background-geolocation/ngx';
import { Alert<PERSON>ontroller, ModalController, PopoverController } from '@ionic/angular';
import { Collection, Map } from 'ol';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
import { Subject } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { MapSwitchComponent } from './ost-map-switch/ost-map-switch.component';
import { LocationProviderService } from '../../service';
import { LocationProviderModalComponent } from './location-provider-modal/location-provider-modal.component';

@Component({
  selector: 'ost-tools-bar',
  templateUrl: './tools-bar.component.html',
  styleUrls: ['./tools-bar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolsBarComponent implements OnInit, OnDestroy {
  // 创建地图
  @Input() map: Map;
  // 创建地图
  @Input() baseLayer: LayerGroup;
  // 基础图层集合
  @Input() baseLayerList: Collection<LayerGroup> = new Collection();
  // 创建业务图层
  @Input() businessLayer!: LayerGroup;
  // 业务图层集合
  @Input() businessLayerList: Collection<BaseLayer> = new Collection();
  // 是否显示定位模式选择按钮
  @Input() showLocationProviderButton = false;
  // 定位监听
  @Output() location: EventEmitter<Coordinates> = new EventEmitter();
  // 刷新图层监听
  @Output() refreshLayer: EventEmitter<any> = new EventEmitter();
  zoomChange$ = new Subject<number>();
  // 地图缩放级别
  zoomLevel: number | undefined;
  // spinner
  spinnerState = false;
  // 当前定位模式
  currentLocationProvider = 3;
  private readonly ngUnsubscribe = new Subject<void>(); // 用于统一管理所有可取消的订阅
  constructor(
    private cd: ChangeDetectorRef,
    private geolocation: Geolocation,
    private backgroundGeolocation: BackgroundGeolocation,
    public popoverController: PopoverController,
    public modalController: ModalController,
    private alertController: AlertController,
    private locationProviderService: LocationProviderService,
  ) { }
  ngOnInit(): void {
    this.setupZoomListener();
    this.setupMapMoveEndListener();
    this.initLocationProvider();
  }

  /**
   * 初始化定位模式
   */
  private initLocationProvider(): void {
    try {
      // 监听定位模式变化
      this.locationProviderService.onProviderChange()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(provider => {
          this.currentLocationProvider = provider;
          this.cd.detectChanges();
        });

      // 设置初始值
      this.currentLocationProvider = this.locationProviderService.getCurrentProvider();
    } catch (error) {
      console.error('初始化定位模式服务失败:', error);
    }
  }

  private setupZoomListener() {
    this.zoomChange$
      .pipe(
        throttleTime(500),
        // distinctUntilChanged(), // 避免连续相同的zoomLevel触发动画
        takeUntil(this.ngUnsubscribe) // 用于在ngOnDestroy中取消订阅
      )
      .subscribe(zoomDelta => {
        this.map.getView().animate({ zoom: this.map.getView().getZoom() + zoomDelta });
      });

    this.zoomLevel = this.map.getView().getZoom();
  }

  private setupMapMoveEndListener() {
    this.map.on('moveend', () => {
      this.zoomLevel = Math.floor(this.map.getView().getZoom() || 0);
      this.cd.detectChanges();
    });
  }

  /**
   * 获取当前位置 - 高精度GPS定位
   */
  async onLocation(): Promise<void> {
    try {
      this.spinnerState = true;

      // 使用用户选择的定位模式配置BackgroundGeolocation
      await this.backgroundGeolocation.configure({
        desiredAccuracy: 1,
        locationProvider: this.currentLocationProvider,
        stationaryRadius: 0,
        distanceFilter: 0,
        interval: 500,
        fastestInterval: 500,
        debug: false
      });

      // 尝试获取高精度定位，减少重试次数和超时时间
      const maxRetries = 2; // 从3次减少到2次
      const targetAccuracy = 10;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const location = await this.backgroundGeolocation.getCurrentLocation({
            timeout: attempt === 1 ? 10000 : 15000, // 缩短超时时间：10秒和15秒
            maximumAge: 0,
            enableHighAccuracy: true
          });

          // 转换为Coordinates格式
          const coords: Coordinates = {
            latitude: location.latitude,
            longitude: location.longitude,
            accuracy: location.accuracy,
            altitude: location.altitude,
            altitudeAccuracy: null,
            heading: location.bearing,
            speed: location.speed
          };

          // 检查精度是否满足要求
          if (location.accuracy <= targetAccuracy) {
            console.log('GPS定位成功:', {
              accuracy: location.accuracy,
              longitude: location.longitude,
              latitude: location.latitude
            });
            this.location.emit(coords);
            return;
          } else if (attempt === maxRetries) {
            // 最后一次尝试，使用当前结果
            console.log('GPS定位完成 (精度:', location.accuracy + 'm)');
            this.location.emit(coords);
            return;
          }

          // 缩短等待时间
          await new Promise(resolve => setTimeout(resolve, 1000)); // 从2秒减少到1秒
        } catch (attemptError) {
          if (attempt === maxRetries) {
            throw attemptError;
          }
          await new Promise(resolve => setTimeout(resolve, 1500)); // 从3秒减少到1.5秒
        }
      }
    } catch (error) {
      console.error('GPS定位失败:', error);

      // 降级方案：使用Ionic Native Geolocation，缩短超时时间
      try {
        const options = {
          enableHighAccuracy: true,
          timeout: 8000, // 从15秒减少到8秒
          maximumAge: 0
        };

        const resp = await this.geolocation.getCurrentPosition(options);
        console.log('降级定位成功 (精度:', resp.coords.accuracy + 'm)');
        this.location.emit(resp.coords);
      } catch (fallbackError) {
        console.error('定位失败:', fallbackError);
      }
    } finally {
      this.spinnerState = false;
    }
  }

  /**
   * 打开图层检测
   */
  async openSwitch(): Promise<void> {
    const opts = {
      component: MapSwitchComponent,
      componentProps: {
        baseLayer: this.baseLayer,
        baseLayerList: this.baseLayerList,
        businessLayer: this.businessLayer,
        businessLayerList: this.businessLayerList
      },
      translucent: true
    };
    const popover = await this.popoverController.create(opts);
    await popover.present();
  }

  /**
   * 打开定位模式选择弹窗
   */
  async openLocationProviderModal(): Promise<void> {
    try {
      const modal = await this.modalController.create({
        component: LocationProviderModalComponent,
        cssClass: 'camera-list-modal',
        backdropDismiss: true
      });

      await modal.present();

      const { data, role } = await modal.onWillDismiss();
      if (role === 'confirm' && data?.selectedProvider) {
        console.log('定位模式已切换为:', data.selectedProvider);
        // 立即应用新的定位模式到当前的定位服务
        await this.applyNewLocationProvider(data.selectedProvider);
      }
    } catch (error) {
      console.error('打开定位模式选择弹窗失败:', error);

      // 如果模态框创建失败，显示一个简单的alert作为备选方案
      const alert = await this.alertController.create({
        header: '定位模式选择',
        message: '当前定位模式: ' + this.getLocationProviderLabel(),
        buttons: ['确定']
      });
      await alert.present();
    }
  }

  /**
   * 立即应用新的定位模式到当前定位服务
   */
  private async applyNewLocationProvider(newProvider: number): Promise<void> {
    try {
      // 使用checkStatus替代已弃用的isLocationEnabled
      const status = await this.backgroundGeolocation.checkStatus();
      if (status.isRunning) {
        console.log('重新配置BackgroundGeolocation定位模式:', newProvider);
        await this.backgroundGeolocation.configure({
          desiredAccuracy: 1,
          locationProvider: newProvider,
          stationaryRadius: 0,
          distanceFilter: 0,
          interval: 500,
          fastestInterval: 500,
          debug: false
        });
      }
    } catch (error) {
      console.warn('应用新定位模式失败:', error);
    }
  }

  /**
   * 获取当前定位模式的图标
   */
  getLocationProviderIcon(): string {
    return this.locationProviderService.getProviderIcon(this.currentLocationProvider);
  }

  /**
   * 获取当前定位模式的标签
   */
  getLocationProviderLabel(): string {
    return this.locationProviderService.getProviderLabel(this.currentLocationProvider);
  }

  /**
   * 调试方法：检查GeolocationConfig同步状态
   */
  async debugGeolocationConfig(): Promise<void> {
    const currentProvider = this.locationProviderService.getCurrentProvider();
    const configProvider = await this.locationProviderService.getCurrentGeolocationConfigProvider();

    console.log('=== 定位配置同步状态检查 ===');
    console.log('当前选择的定位模式:', currentProvider);
    console.log('GeolocationConfig中的定位模式:', configProvider);
    console.log('是否同步:', currentProvider === configProvider ? '✅ 已同步' : '❌ 未同步');
    console.log('================================');
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.zoomChange$.unsubscribe();
  }
}
