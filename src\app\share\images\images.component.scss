.imgItem {
  border: 1px dashed #eee;
  position: relative;
  padding: 0;
  width: 87.5px;
  height: 91.5px;
  display: flex; // 使用 flex 辅助居中
  align-items: center;
  justify-content: center;

  // 图片样式
  &__image {
    width: 85px;
    height: 89px;
    object-fit: cover; // 确保图片不变形
  }

  // “添加”图标样式
  &__add-icon {
    padding: 25px;
    box-sizing: border-box; // 让 padding 不会撑大元素
    width: 100%;
    height: 100%;
  }
}

.delete-icon {
  position: absolute;
  top: 0px;
  right: 0px;
  font-size: 24px;
  color: red;
  cursor: pointer; // 增加手型光标
  background-color: white; // 添加背景色，避免与图片重叠
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  z-index: 11; // 保证高于优化中遮罩层
}

// 上传状态的基类 (抽取出的公共样式)
.upload-status {
  position: absolute;
  width: 100%; // 宽度占满父容器，配合 text-align
  bottom: 0; // 贴在底部
  left: 50%; // 修正后的标准居中方式
  transform: translateX(-50%); // 修正后的标准居中方式
  color: #fff;
  font-size: 12px;
  text-align: center;
  box-sizing: border-box;

  // 使用修饰符类来定义不同状态的颜色和动画
  &--pending {
    background-color: #bdbdbd; // 灰色
  }

  &--failed {
    background-color: #ff4d4f; // 红色
  }

  &--success {
    background-color: #4CAF50; // 绿色
  }

  &--uploading {
    background-color: #FF9800; // 橙色
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}

// 优化中遮罩层样式
.imgItem__optimizing-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  border-radius: 4px;
}
.optimizing-text {
  color: #fff;
  font-size: 12px;
  letter-spacing: 1px;
}