<!-- 事件上报 -->
<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="ion-text-center ion-no-padding">事项上报</ion-title>
    <ion-buttons slot="end">
      <ion-button>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<!-- <ion-segment [(ngModel)]="selectedTab" class="tab-segment">
  <ion-segment-button value="basic">
    <ion-label>基础信息</ion-label>
  </ion-segment-button>
  <ion-segment-button value="logs">
    <ion-label>节点信息</ion-label>
  </ion-segment-button>
</ion-segment> -->

<ion-content [ngClass]="{'has-footer': isCanRemove()}">
  <evreport-basic [hidden]="selectedTab!=='basic'"
    [modelInfo]="modelInfo"
    [modelMode]="modelMode" 
    (eventCodeChange)="eventCodeChange($event)"
    [coordinate]="coordinate">
  </evreport-basic>

  <!-- <evreport-logs [hidden]="selectedTab!=='logs'" 
    [modelMode]="modelMode" 
    [basicInfo]="basicInfo"
    [isModal]="isModal"
    [coordinate]="coordinate">
  </evreport-logs> -->
</ion-content>

<!-- 底部按钮 -->
<ion-footer *ngIf="isCanRemove()">
  <ion-toolbar>
    <ion-button expand="block" color="success" (click)="remove()">消除隐患</ion-button>
  </ion-toolbar>
</ion-footer>