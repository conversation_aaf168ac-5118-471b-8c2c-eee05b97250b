import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { KeypointPage } from './keypoint.page';

const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./keypoint-view/keypoint-view.module').then(m => m.KeypointViewPageModule)
  },
  {
    path: 'manage',
    component: KeypointPage,
  },
  {
    path: 'confirm',
    loadChildren: () => import('./keypoint-confirm/keypoint-confirm.module').then(m => m.KeypointConfirmPageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class KeypointPageRoutingModule { }