import { DatePipe } from '@angular/common';
import { Component, HostListener, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ModalController, NavController } from '@ionic/angular';
import { StatisticsService } from './statistics.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { EventReportInfo } from '../home/<USER>/home';
import { IncidentListComponent } from './incident-list/incident-list.component';

@Component({
  selector: 'app-statistics',
  templateUrl: './statistics.page.html',
  styleUrls: ['./statistics.page.scss']
})
export class StatisticsPage implements OnInit, OnDestroy {
  // 是否展示搜索表单
  isShowSearch = false;
  // 搜索表单
  searchGroup: FormGroup;
  selectedTab = 'inspection';
  // 站点反馈问题统计
  problemList = EventReportInfo;
  // 站点巡检任务完成率统计
  depStatistics = [];
  // 巡检人员合格率统计
  userStatistics = [];
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    public nav: NavController, private fb: FormBuilder,
    private datePipe: DatePipe,
    public netSer: StatisticsService,
    public modalController: ModalController,
  ) { }

  ngOnInit() {
    // 初始化搜索表单
    this.searchGroup = this.fb.group({
      depCode: [],
      startDate: [],
      endDate: [],
    });
    this.loadStatisticsData();
  }

  /**
   * 获取统计数据
   */
  loadStatisticsData(formData?: any) {
    const operate = formData === undefined ? this.netSer.getStatisticsData() : this.netSer.getStatisticsData(formData);
    operate.pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res.code === 0) {
          this.depStatistics = res.data.depStatistics;
          this.problemList = res.data.eventStatistics;
          this.userStatistics = res.data.userStatistics;
        }
      });
  }

  selectTab(tab) {
    this.selectedTab = tab;
  }

  /**
   * 搜索
   */
  onSearch(): void {
    const formData = this.searchGroup.value;
    Object.keys(formData).map(item => {
      if (formData[item] === null) {
        formData[item] = '';
      }
      return formData;
    });
    // 日期格式化
    if (this.searchGroup.value.startDate) {
      formData.startDate = this.datePipe.transform(formData.startDate, 'yyyy-MM-dd');
    }
    if (this.searchGroup.value.endDate) {
      formData.endDate = this.datePipe.transform(formData.endDate, 'yyyy-MM-dd');
    }
    this.loadStatisticsData(formData);
  }

  /**
   * 重置
   */
  onReset(): void {
    this.searchGroup.reset();
  }

  /**
   * 筛选
   */
  onShowSearch(): void {
    this.isShowSearch = !this.isShowSearch;
  }

  async showDetail(): Promise<void> {
    const startTime = this.searchGroup.value.startDate ? this.searchGroup.value.startDate : '';
    const endTime = this.searchGroup.value.endDate ? this.searchGroup.value.endDate : '';
    const modal = await this.modalController.create({
      component: IncidentListComponent,
      componentProps: {
        startTime, endTime,
      },
      backdropDismiss: false
    });
    await modal.present();
  }

  /**
   * 回退
   */
  goBack(): void {
    this.nav.back();
  }
  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.nav.back();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}
