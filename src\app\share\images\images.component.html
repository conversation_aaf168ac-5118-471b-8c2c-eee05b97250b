<div class="ion-margin-top ion-margin-start" style="font-size: 14px;">{{label}}:</div>

<ion-grid>
  <ion-row class="ion-align-items-center">
    <ng-container *ngIf="base64Image.length > 0; else noData">
      <ng-container *ngFor="let item of base64Image; let i = index">
        <ion-col size="3" class="imgItem">
          <img class="imgItem__image" [src]="item.base64 ? item.base64 : item.url" (click)="previewAvatar(item)">
          
          <!-- 优化中遮罩 -->
          <div *ngIf="item.status === 'optimizing'" class="imgItem__optimizing-mask">
            <span class="optimizing-text">图片处理中...</span>
          </div>

          <span class="delete-icon" (click)="onDelete(i)" *ngIf="modelMode !== DetailsMode.SEE">&times;</span>

          <div [ngSwitch]="item.status" class="upload-status-container">
            <span *ngSwitchCase="'pending'" class="upload-status upload-status--pending">待上传</span>
            <span *ngSwitchCase="'uploading'" class="upload-status upload-status--uploading">上传中...</span>
            <span *ngSwitchCase="'success'" class="upload-status upload-status--success">上传成功</span>
            <span *ngSwitchCase="'failed'" class="upload-status upload-status--failed">上传失败</span>
          </div>
        </ion-col>
      </ng-container>

      <ion-col size="3" class="imgItem" (click)="presentActionSheet()" *ngIf="modelMode !== DetailsMode.SEE && (!maxCount || base64Image.length < maxCount)">
        <img class="imgItem__add-icon" src="/assets/images/uploader/plus.png">
      </ion-col>
    </ng-container>
  </ion-row>
</ion-grid>

<ng-template #noData>
  <ng-container *ngIf="modelMode === DetailsMode.SEE">
    <ion-col size="3" class="imgItem">
      <div class="no-data">暂无数据</div>
    </ion-col>
  </ng-container>
  <ng-container *ngIf="modelMode !== DetailsMode.SEE">
    <ion-col size="3" class="imgItem" (click)="presentActionSheet()" *ngIf="!maxCount || base64Image.length < maxCount">
      <img class="imgItem__add-icon" src="/assets/images/uploader/plus.png">
    </ion-col>
  </ng-container>
</ng-template>