.confirm-list-container {
  height: 100%;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--ion-color-medium);
  
  ion-icon {
    font-size: 64px;
    margin-bottom: 16px;
    color: var(--ion-color-success);
  }
  
  p {
    margin: 0;
    font-size: 16px;
    text-align: center;
    color: var(--ion-color-medium);
  }
}

.points-list {
  padding: 0;
  
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 100px;
    border-bottom: 1px solid var(--ion-color-light);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    
    &:last-child {
      border-bottom: none;
    }
    
    // 添加点击提示图标
    &::after {
      content: '';
      position: absolute;
      right: 80px;
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      height: 16px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="%23666" d="M256 0c17.7 0 32 14.3 32 32V66.7C368.4 80.1 431.9 143.6 445.3 224H480c17.7 0 32 14.3 32 32s-14.3 32-32 32H445.3C431.9 368.4 368.4 431.9 288 445.3V480c0 17.7-14.3 32-32 32s-32-14.3-32-32V445.3C143.6 431.9 80.1 368.4 66.7 288H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H66.7C80.1 143.6 143.6 80.1 224 66.7V32c0-17.7 14.3-32 32-32zM352 256c0 53-43 96-96 96s-96-43-96-96s43-96 96-96s96 43 96 96zm-96-64c-35.3 0-64 28.7-64 64s28.7 64 64 64s64-28.7 64-64s-28.7-64-64-64z"/></svg>') no-repeat center;
      background-size: contain;
      opacity: 0.4;
      transition: opacity 0.2s ease;
    }
    
    &.selected {
      --background: var(--ion-color-primary-tint);
      --color: var(--ion-color-primary-contrast);
      border-left: 4px solid var(--ion-color-primary);
      
      .point-name {
        color: var(--ion-color-primary-contrast);
      }
      
      &::after {
        opacity: 0.8;
      }
    }
    
    &:hover:not(.selected) {
      --background: var(--ion-color-light-tint);
      
      &::after {
        opacity: 0.6;
      }
    }
    
    &:active {
      transform: scale(0.98);
    }
    
    ion-checkbox {
      margin-right: 12px;
      
      &::part(container) {
        border-radius: 4px;
      }
    }
  }
}

.point-info {
  margin: 12px 0;
  
  .point-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--ion-color-dark);
    line-height: 1.3;
  }
  
  .point-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-item {
    font-size: 13px;
    color: var(--ion-color-medium);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 6px;
    line-height: 1.4;
    
    ion-icon {
      font-size: 14px;
      min-width: 14px;
      flex-shrink: 0;
    }
    
    span {
      flex: 1;
    }
  }
}

.rain-badge {
  margin-left: 8px;
  padding: 2px 8px;
  background: var(--ion-color-warning-tint);
  color: var(--ion-color-warning-contrast);
  border-radius: 12px;
  font-size: 11px;
  display: inline-flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
  
  ion-icon {
    font-size: 12px;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-left: 8px;
  
  .action-btn {
    margin: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: all 0.2s ease;
    
    ion-icon {
      font-size: 20px;
    }
    
    &:hover {
      transform: scale(1.1);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  .approve-btn {
    --background-hover: var(--ion-color-success-tint);
    
    &:hover {
      --background: var(--ion-color-success-tint);
      --color: var(--ion-color-success-contrast);
    }
  }
  
  .reject-btn {
    --background-hover: var(--ion-color-danger-tint);
    
    &:hover {
      --background: var(--ion-color-danger-tint);
      --color: var(--ion-color-danger-contrast);
    }
  }
}

// 选中动画
@keyframes selectPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--ion-color-primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--ion-color-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--ion-color-primary-rgb), 0);
  }
}

ion-item.selected {
  animation: selectPulse 0.6s ease-out;
}

// 响应式设计
@media (max-width: 768px) {
  .action-buttons {
    .action-btn {
      width: 36px;
      height: 36px;
      
      ion-icon {
        font-size: 18px;
      }
    }
  }
  
  .point-info {
    .point-name {
      font-size: 15px;
    }
    
    .detail-item {
      font-size: 12px;
    }
  }
}

// 滚动条样式
.confirm-list-container::-webkit-scrollbar {
  width: 4px;
}

.confirm-list-container::-webkit-scrollbar-track {
  background: var(--ion-color-light);
}

.confirm-list-container::-webkit-scrollbar-thumb {
  background: var(--ion-color-medium);
  border-radius: 2px;
}

.confirm-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--ion-color-dark);
}