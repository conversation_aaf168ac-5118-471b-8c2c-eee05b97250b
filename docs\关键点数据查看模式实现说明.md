# 关键点数据查看模式实现说明

## 功能概述

实现了关键点数据状态列的查看模式功能，当用户点击数据状态图标时，会以查看模式打开打卡模态框组件，自动回显已有数据并设置为只读状态。

## 实现细节

### 1. 打卡模态框组件增强

#### 新增属性
```typescript
// 数据模式（新增或查看）
@Input() modelMode: DetailsMode = DetailsMode.ADD;
// 是否为详情模式
@Input() isDetailMode: boolean = false;
```

#### 标题动态化
```typescript
get currentTabTitle(): string {
  const isViewMode = this.modelMode === DetailsMode.SEE;
  if (this.activeTab === 'clockIn') {
    return isViewMode ? '查看打卡数据' : '关键点打卡';
  } else {
    return isViewMode ? '查看未巡检原因' : '未巡检原因上报';
  }
}
```

### 2. 打卡组件查看模式支持

#### 新增属性
```typescript
// 数据模式（新增或查看）
@Input() modelMode: DetailsMode = DetailsMode.ADD;
// 是否为详情模式
@Input() isDetailMode: boolean = false;
```

#### 数据回显功能
```typescript
ngOnInit() {
  // 如果是查看模式且有打卡数据，回显数据
  if (this.modelMode === DetailsMode.SEE && this.currentKeyPoint?.picCode) {
    this.loadExistingData();
  }
}

private loadExistingData(): void {
  if (this.currentKeyPoint?.picCode) {
    // 在查看模式下，使用fileCodes来显示已有图片，不影响base64Images
    if (Array.isArray(this.currentKeyPoint.picCode)) {
      this.fileCodes = [...this.currentKeyPoint.picCode];
    } else if (typeof this.currentKeyPoint.picCode === 'string') {
      this.fileCodes = [this.currentKeyPoint.picCode];
    }
  }
}
```

#### 查看模式界面控制
```html
<!-- 查看模式下内容区占满100%高度 -->
<ion-content [style.height]="isViewMode ? '100%' : 'calc(100% - 56px)'">
  <!-- 内容区域 -->
</ion-content>

<!-- 统一的图片组件，根据模式动态切换uploadMode -->
<ost-images
  [modelMode]="modelMode"
  [uploadMode]="isViewMode ? 'fileCode' : 'base64'"
  [fileCodes]="fileCodes"
  [base64Images]="base64Images"
  [cameraOnly]="true"
  (base64ImagesChange)="onBase64ImagesChange($event)">
</ost-images>

<!-- 底部统一提交按钮 -->
<ion-footer *ngIf="!isViewMode">
  <!-- 提交按钮内容 -->
</ion-footer>
```

### 3. 未巡检原因组件查看模式支持

#### 数据回显功能
```typescript
ngOnInit() {
  // 如果是查看模式且有未巡检原因，回显数据
  if (this.modelMode === DetailsMode.SEE && this.currentKeyPoint?.reason) {
    this.loadExistingData();
  }
}

private loadExistingData(): void {
  if (this.currentKeyPoint?.reason) {
    this.reasonText = this.currentKeyPoint.reason;
  }
  // 回显雨天不巡查标识
  if (this.currentKeyPoint?.isItRaining !== undefined) {
    this.isItRaining = this.currentKeyPoint.isItRaining === '是' || 
                      this.currentKeyPoint.isItRaining === true;
  }
}
```

#### 只读状态控制
```html
<ion-textarea
  [(ngModel)]="reasonText"
  [placeholder]="isViewMode ? '' : '请输入未巡检原因'"
  [readonly]="isViewMode"
  rows="3">
</ion-textarea>

<ion-toggle 
  slot="end" 
  [(ngModel)]="isItRaining" 
  [disabled]="isViewMode">
</ion-toggle>
```

### 4. 关键点组件调用方式

```typescript
async onDataStatusClick(keyPoint: any, event: Event, tabType: 'clockIn' | 'reason'): Promise<void> {
  event.stopPropagation();
  
  await this.modalManagerService.createModalWithGuard('punchIn', {
    component: ClockInModalComponent,
    cssClass: 'camera-list-modal',
    componentProps: {
      taskCode: this.taskCode,
      currentKeyPoint: keyPoint,
      initialTab: tabType,
      modelMode: DetailsMode.SEE, // 设置为查看模式
      isDetailMode: true, // 设置为详情模式
    },
  }, (data) => {
    // 处理回调...
  });
}
```

## 用户体验

### 查看打卡数据
1. 点击绿色相机图标
2. 打开"查看打卡数据"页面
3. 自动显示已上传的打卡照片
4. 所有控件为只读状态
5. 隐藏提交按钮
6. 内容区占满100%高度，提供更好的查看体验

### 查看未巡检原因
1. 点击橙色警告图标
2. 打开"查看未巡检原因"页面
3. 自动显示未巡检原因文本
4. 显示雨天不巡查状态
5. 所有控件为只读状态
6. 隐藏提交按钮
7. 内容区占满100%高度，提供更好的查看体验

## 数据格式支持

### 打卡照片编码字段
- **字符串数组**: `["fileCode1", "fileCode2"]` (查看模式使用fileCodes)
- **单个字符串**: `"fileCode"` (查看模式使用fileCodes)
- **Base64数组**: `["base64_image_1", "base64_image_2"]` (编辑模式使用base64Images)

### 未巡检原因字段
- **字符串**: `"设备故障，无法进行巡检"`

### 雨天不巡查字段
- **字符串**: `"是"` 或 `"否"`
- **布尔值**: `true` 或 `false`

## 技术优势

1. **数据完整性**: 完整回显所有相关数据
2. **用户体验**: 只读模式避免误操作
3. **界面一致性**: 复用现有组件，保持界面统一
4. **扩展性**: 支持多种数据格式
5. **安全性**: 查看模式下无法修改或提交数据
6. **数据隔离**: 查看模式使用fileCodes，编辑模式使用base64Images，避免数据混淆
7. **代码简洁**: 使用单个图片组件，通过动态属性切换模式，减少重复代码

## 测试建议

1. 测试不同格式的打卡照片数据回显（fileCodes模式）
2. 测试未巡检原因文本的完整显示
3. 测试雨天不巡查状态的正确回显
4. 验证查看模式下所有控件的只读状态
5. 确认提交按钮在查看模式下被正确隐藏
6. 验证查看模式和编辑模式下图片组件的正确切换
7. 确认base64Images在查看模式下不受影响
