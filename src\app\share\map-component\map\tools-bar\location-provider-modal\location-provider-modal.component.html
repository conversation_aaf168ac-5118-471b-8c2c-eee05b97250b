<ion-header>
  <ion-toolbar color="primary">
    <ion-title>定位模式选择</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="dismiss()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="location-provider-modal">
  <div class="provider-options">
    <div class="option-header">
      <ion-text color="dark">
        <p>选择适合您当前环境的定位模式</p>
      </ion-text>
    </div>

    <ion-list>
      <ion-item 
        *ngFor="let option of locationProviderOptions" 
        button 
        (click)="selectProvider(option.value)"
        [class.selected]="currentProvider === option.value">
        
        <ion-icon 
          [name]="option.icon" 
          slot="start" 
          [color]="currentProvider === option.value ? 'primary' : 'medium'">
        </ion-icon>
        
        <ion-label>
          <h3 [ngClass]="{'text-primary': currentProvider === option.value}">
            {{ option.label }}
            <ion-icon 
              *ngIf="currentProvider === option.value" 
              name="checkmark-circle" 
              color="primary" 
              class="selected-icon">
            </ion-icon>
          </h3>
          <p>{{ option.description }}</p>
        </ion-label>
      </ion-item>
    </ion-list>

    <div class="recommendation-note">
      <ion-card>
        <ion-card-content>
          <div class="note-header">
            <ion-icon name="information-circle-outline" color="primary"></ion-icon>
            <ion-text color="primary">
              <strong>推荐说明</strong>
            </ion-text>
          </div>
          <ion-text color="medium">
            <p>• <strong>室外巡检</strong>：推荐使用GPS定位或混合定位</p>
            <p>• <strong>室内巡检</strong>：推荐使用网络定位或混合定位</p>
            <p>• <strong>一般情况</strong>：推荐使用混合定位（默认）</p>
          </ion-text>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>
