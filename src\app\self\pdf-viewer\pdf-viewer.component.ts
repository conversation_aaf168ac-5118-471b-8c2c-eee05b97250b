import { Component, HostListener, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { SimplePdfViewerComponent } from 'simple-pdf-viewer';
@Component({
  selector: 'app-pdf-viewer',
  templateUrl: './pdf-viewer.component.html',
  styleUrls: ['./pdf-viewer.component.scss']
})
// 使用手册
export class PdfViewersComponent {
  @ViewChild(SimplePdfViewerComponent) private pdfViewer: SimplePdfViewerComponent;
  src = '../../../assets/pdf/';
  constructor(
    public modalController: ModalController,
  ) { }


  /**
   * 返回
   */
  goBack(): void {
    this.modalController.dismiss();
  }


  onLoadComplete(): void {
    this.pdfViewer.zoomFullPage();
  }

  /**
   * 滑动退出
   */
  @HostListener('document:ionBackButton', ['$event'])
  backButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalController.dismiss();
    });
  }
}
