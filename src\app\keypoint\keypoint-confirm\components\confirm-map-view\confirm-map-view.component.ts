import { Component, Input, OnInit, OnChanges, SimpleChanges, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { UnconfirmedKeyPoint } from '../../keypoint-confirm.service';
import { MapComponent } from '../../../../share/map-component/map/map.component';

@Component({
  selector: 'app-confirm-map-view',
  templateUrl: './confirm-map-view.component.html',
  styleUrls: ['./confirm-map-view.component.scss']
})
export class ConfirmMapViewComponent implements OnInit, OnChanges {
  @ViewChild('mapComponent') mapComponent: MapComponent;
  @ViewChild('mapContainer') mapContainer: ElementRef;

  @Input() points: UnconfirmedKeyPoint[] = [];

  mapReady = false;
  layerIds: string[] = ['p_pipe_joint_info', 'unconfirmed_points'];

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    // 地图加载完成会通过mapLoaded事件触发onMapReady方法
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['points'] && this.mapReady && this.points.length > 0) {
      this.updateMapPoints();
      // 当关键点数据更新时，自动定位到所有关键点范围
      setTimeout(() => {
        this.fitToAllPoints();
      }, 300);
    }
  }
  /**
   * 地图准备就绪事件
   */
  onMapReady() {
    this.mapReady = true;
    this.updateMapPoints();
    // 自动定位到所有待确认关键点的范围
    setTimeout(() => {
      this.fitToAllPoints();
    }, 500);
    this.cdr.detectChanges();
  }

  /**
   * 更新地图上的关键点显示
   */
  private updateMapPoints() {
    if (this.mapComponent && this.mapReady && this.points) {
      // 转换数据格式以适配地图组件
      const mapPoints = this.points.map(point => {
          let longitude = point.longitude;
          let latitude = point.latitude;
          
          // 如果没有直接的坐标信息，从geom字段解析
          if (!longitude || !latitude) {
            try {
              const geom = JSON.parse(point.geom);
              if (geom.type === 'Point' && geom.coordinates && geom.coordinates.length >= 2) {
                longitude = geom.coordinates[0];
                latitude = geom.coordinates[1];
              }
            } catch (error) {
              console.warn('解析关键点坐标失败:', point.pointCode, error);
            }
          }
        
        // KeyPointRenderer期望的数据格式
        const mapPoint = {
          id: point.pointCode,
          pointName: point.pointName,
          point: JSON.stringify([longitude, latitude]), // KeyPointRenderer期望的坐标格式
          state: '未确认', // 统一显示为未确认状态
          bufferTrans: point.bufferTrans || 0, // 缓冲范围
          ...point
        };
        
        return mapPoint;
      });
      
      // 调用地图组件的setKeyPoints方法
      this.mapComponent.setKeyPoints(mapPoints);
    }
  }

  /**
   * 缩放到所有关键点
   */
  fitToAllPoints() {
    if (this.mapComponent && this.mapComponent.map && this.points.length > 0) {
      try {
        // 计算所有关键点的边界范围
        const coordinates: number[][] = [];
        
        this.points.forEach(point => {
          let longitude = point.longitude;
          let latitude = point.latitude;
// 如果没有直接的坐标信息，从geom字段解析
          if (!longitude || !latitude) {
            try {
              const geom = JSON.parse(point.geom);
              if (geom.type === 'Point' && geom.coordinates && geom.coordinates.length >= 2) {
                longitude = geom.coordinates[0];
                latitude = geom.coordinates[1];
              }
            } catch (error) {
              console.warn('解析关键点坐标失败:', point.pointCode, error);
            }
          }
          
          if (longitude && latitude && longitude !== 0 && latitude !== 0) {
            coordinates.push([longitude, latitude]);
          }
        });
        
        if (coordinates.length > 0) {
          // 计算边界范围
          const lons = coordinates.map(coord => coord[0]);
          const lats = coordinates.map(coord => coord[1]);
          const minLon = Math.min(...lons);
          const maxLon = Math.max(...lons);
          const minLat = Math.min(...lats);
          const maxLat = Math.max(...lats);
          
          // 添加一些边距
          const padding = 0.001; // 约100米的边距
          const extent = [
            minLon - padding,
            minLat - padding,
            maxLon + padding,
            maxLat + padding
          ];
          

          
          // 使用地图视图的fit方法缩放到计算的范围
          this.mapComponent.map.getView().fit(extent, {
            duration: 1000,
            padding: [50, 50, 50, 50],
            maxZoom: 18
          });
        }
      } catch (error) {
        console.error('缩放到关键点范围失败:', error);
      }
    }
  }


}