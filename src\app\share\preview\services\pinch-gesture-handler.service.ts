import { Injectable } from '@angular/core';
import { GestureConfig } from './image-gesture.model';

/**
 * 缩放手势处理器
 * 负责处理图片的缩放手势
 */
@Injectable({
  providedIn: 'root'
})
export class PinchGestureHandlerService {
  private lastScale = 1;

  /**
   * 处理缩放开始
   */
  handlePinchStart(
    ev: any,
    onClearCache: () => void,
    onStopInertia: () => void
  ): void {
    // 停止任何正在进行的惯性滑动
    onStopInertia();

    // 清除缓存以获取最新尺寸
    onClearCache();

    // 防止页面缩放
    if (ev.preventDefault) {
      ev.preventDefault();
    }
  }

  /**
   * 处理缩放手势
   */
  handlePinch(
    ev: any,
    config: GestureConfig,
    onClearCache: () => void,
    onConstrainTranslation: () => void
  ): number {
    // 使用更平滑的缩放计算
    let newScale = this.lastScale * ev.scale;

    // 添加缩放平滑处理，避免突然的跳跃
    const scaleDiff = Math.abs(newScale - this.lastScale);
    if (scaleDiff > 0.5) {
      // 如果缩放变化过大，进行平滑处理
      const direction = newScale > this.lastScale ? 1 : -1;
      newScale = this.lastScale + (direction * 0.1);
    }

    newScale = Math.max(config.minScale!, Math.min(config.maxScale!, newScale));

    // 性能优化：缩放时清除缓存，因为图片尺寸发生了变化
    onClearCache();

    // 在缩放过程中的约束处理由调用方决定
    onConstrainTranslation();

    return newScale;
  }

  /**
   * 处理缩放结束
   */
  handlePinchEnd(
    currentScale: number,
    onConstrainTranslation: () => void
  ): void {
    this.lastScale = currentScale;

    // 缩放结束后，检查并调整当前位移是否在有效范围内
    onConstrainTranslation();
  }

  /**
   * 重置缩放状态
   */
  resetScale(): void {
    this.lastScale = 1;
  }

  /**
   * 获取当前缩放基准值
   */
  getLastScale(): number {
    return this.lastScale;
  }

  /**
   * 设置缩放基准值
   */
  setLastScale(scale: number): void {
    this.lastScale = scale;
  }

  /**
   * 约束缩放值在配置范围内
   */
  constrainScale(scale: number, config: GestureConfig): number {
    return Math.max(config.minScale!, Math.min(config.maxScale!, scale));
  }

  /**
   * 检查缩放值是否有效
   */
  isValidScale(scale: number, config: GestureConfig): boolean {
    return scale >= config.minScale! && scale <= config.maxScale!;
  }

  /**
   * 计算缩放增量
   */
  calculateScaleDelta(currentScale: number, targetScale: number): number {
    return targetScale - currentScale;
  }

  /**
   * 平滑缩放到目标值
   */
  smoothScaleTo(
    currentScale: number,
    targetScale: number,
    config: GestureConfig,
    onUpdate: (scale: number) => void,
    duration: number = 300
  ): void {
    const constrainedTarget = this.constrainScale(targetScale, config);
    const startScale = currentScale;
    const deltaScale = constrainedTarget - startScale;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const easeProgress = this.easeOutCubic(progress);
      const newScale = startScale + deltaScale * easeProgress;
      
      onUpdate(newScale);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.lastScale = constrainedTarget;
      }
    };

    animate();
  }

  /**
   * 缓动函数：缓出三次方
   */
  private easeOutCubic(t: number): number {
    return 1 - Math.pow(1 - t, 3);
  }

  /**
   * 获取缩放状态信息
   */
  getScaleInfo(config: GestureConfig): {
    lastScale: number;
    minScale: number;
    maxScale: number;
    scaleRange: number;
  } {
    return {
      lastScale: this.lastScale,
      minScale: config.minScale!,
      maxScale: config.maxScale!,
      scaleRange: config.maxScale! - config.minScale!
    };
  }
}
