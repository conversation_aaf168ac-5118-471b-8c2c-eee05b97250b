<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>个人</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="body">
  <ion-item lines="full" class="split-line">
    <ion-avatar slot="start">
      <img [src]="userAvatar" />
    </ion-avatar>
    <ion-label>
      {{userSer.userName}}
      <p>{{userSer.phoneNumber}}</p>
    </ion-label>
    <!-- 部门 -->
    <ion-note slot="end" class="self-note-end">
      <span>
        部门： {{depName}}
      </span>
    </ion-note>
  </ion-item>

  <!-- 修改密码 -->
  <ion-item lines="none" class="full-border" (click)="changePassword()">
    <ion-note slot="start" color="primary" class="note-start">
      <ion-icon name="bag-outline"></ion-icon>
    </ion-note>
    <ion-label> 修改密码 </ion-label>
    <ion-note slot="end" class="note-end">
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-note>
  </ion-item>

  <!-- GIS配置 -->
  <!-- <ion-item lines="none" class="full-border" (click)="gisConfig()">
    <ion-note slot="start" color="primary" class="note-start">
      <ion-icon name="earth-outline"></ion-icon>
    </ion-note>
    <ion-label> GIS配置 </ion-label>
    <ion-note slot="end" class="note-end">
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-note>
  </ion-item> -->

  <!-- 数据同步管理 -->
  <ion-item lines="none" class="full-border" (click)="openDataSyncManager()">
    <ion-note slot="start" color="success" class="note-start">
      <ion-icon name="sync-outline"></ion-icon>
    </ion-note>
    <ion-label> 数据同步管理 </ion-label>
    <ion-badge *ngIf="failedDataCount > 0" color="danger" slot="end">{{failedDataCount}}</ion-badge>
    <ion-note slot="end" class="note-end">
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-note>
  </ion-item>

  <!-- 检查更新 -->
  <ion-item lines="none" class="full-border" (click)="checkUpdate()">
    <ion-note slot="start" color="warning" class="note-start">
      <ion-icon name="cloud-upload-outline"></ion-icon>
    </ion-note>
    <ion-label> 检查更新 </ion-label>
    <ion-note slot="end">{{environmentVersion}}</ion-note>
    <ion-note slot="end" class="note-end">
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-note>
  </ion-item>

  <!-- 使用手册 -->
  <!-- <ion-item lines="none" class="full-border" (click)="usePDF()">
    <ion-note slot="start" color="primary" class="note-start">
      <ion-icon name="document-outline"></ion-icon>
    </ion-note>
    <ion-label> 使用手册 </ion-label>
    <ion-note slot="end" class="note-end">
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-note>
  </ion-item> -->

  <!-- 退出登陆 -->
  <ion-item lines="none" class="login-out split-line" (click)="loginOut()">
    <ion-label> 退出登陆 </ion-label>
  </ion-item>

  <!-- 震动测试 -->
  <!-- <ion-item lines="none" class="full-border" (click)="testVibration()">
    <ion-note slot="start" color="danger" class="note-start">
      <ion-icon name="megaphone-outline"></ion-icon>
    </ion-note>
    <ion-label> 震动测试 </ion-label>
    <ion-note slot="end" class="note-end">
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-note>
  </ion-item> -->
</ion-content>