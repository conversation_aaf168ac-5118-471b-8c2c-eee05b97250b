import { Injectable } from '@angular/core';
import { BackgroundGeolocation, BackgroundGeolocationConfig, BackgroundGeolocationEvents, BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { Subscription } from 'rxjs';
import { StorageService } from '../../@core/providers/storage.service';
import { UserInfoService } from '../../@core/providers/user-Info.service';
import { LocationProviderService } from '../../share/map-component/service';

@Injectable({
  providedIn: 'root'
})
// 定位服务
export class LocationService {
  private locationSubscription: Subscription;
  private providerChangeSubscription: Subscription;
  private currentTaskCode: string;
  private currentGroupCode: string;
  private isTrackingActive = false;

  constructor(
    private backgroundGeolocation: BackgroundGeolocation,
    private storage: StorageService,
    private userSer: UserInfoService,
    private locationProviderService: LocationProviderService
  ) {
    // 监听定位模式变化，动态更新配置
    this.providerChangeSubscription = this.locationProviderService.onProviderChange().subscribe(newProvider => {
      this.onLocationProviderChanged(newProvider);
    });
  }

  /**
   * 定位模式变化处理
   */
  private async onLocationProviderChanged(newProvider: number): Promise<void> {
    if (this.isTrackingActive) {
      console.log('巡检过程中定位模式已切换为:', newProvider);
      try {
        // 重新配置BackgroundGeolocation以应用新的定位模式
        const config = this.createPostTemplate(this.currentTaskCode, this.currentGroupCode);
        await this.backgroundGeolocation.configure(config);
        console.log('轨迹跟踪服务已更新定位模式');
      } catch (error) {
        console.error('更新轨迹跟踪定位模式失败:', error);
      }
    }
  }

  /**
   * 初始化定位配置
   * @param taskCode 任务代码
   * @param groupCode 组代码
   */
  async initLocationConfigure(taskCode: string, groupCode: string): Promise<void> {
    console.log('初始化定位插件配置', taskCode, groupCode);

    this.currentTaskCode = taskCode;
    this.currentGroupCode = groupCode;

    // 获取存储的配置
    const options = await this.storage.get('GeolocationConfig').toPromise();
    console.log('初始化服务', options);

    // 配置定位服务
    await this.backgroundGeolocation.configure(options);
    await this.backgroundGeolocation.configure(this.createPostTemplate(taskCode, groupCode));
    await this.backgroundGeolocation.deleteAllLocations();
  }

  /**
   * 开始位置监听
   * @param onLocationChange 位置变化回调
   */
  startLocationTracking(onLocationChange: (location: BackgroundGeolocationResponse) => void): void {
    this.isTrackingActive = true;
    this.locationSubscription = this.backgroundGeolocation.on(BackgroundGeolocationEvents.location)
      .subscribe(onLocationChange);
  }

  /**
   * 停止位置监听
   */
  stopLocationTracking(): void {
    this.isTrackingActive = false;
    if (this.locationSubscription) {
      this.locationSubscription.unsubscribe();
      this.locationSubscription = null;
    }
  }

  /**
   * 创建位置数据上传模板
   * @param taskCode 任务代码
   * @param groupCode 组代码
   */
  private createPostTemplate(taskCode: string, groupCode: string): BackgroundGeolocationConfig {
    // 使用用户选择的定位模式
    const userLocationProvider = this.locationProviderService.getCurrentProvider();

    return {
      locationProvider: userLocationProvider,
      // 实时上传位置数据的端点
      url: `${ResourceGlobalConfig.url}/work-inspect/api/v2/inspect/app/trajectory/upload`,
      syncUrl: `${ResourceGlobalConfig.url}/work-inspect/api/v2/inspect/app/trajectory/upload`,
      httpHeaders: {
        Authorization: this.userSer.token
      },
      postTemplate: {
        longitude: '@longitude', // 经度
        latitude: '@latitude', // 纬度
        speed: '@speed',
        trajectoryTime: '@time', // 轨迹时间
        altitude: '@altitude', // 高程
        accuracy: '@accuracy', // 精度
        userName: this.userSer.userName, //巡检人名称
        userCode: this.userSer.userId, // 巡检人编号
        depCode: this.userSer.depCode, // 部门编号
        depName: this.userSer.depName, // 部门名称
        groupCode,
        taskCode,
      },
      debug: false,
    };
  }

  /**
   * 获取当前位置坐标
   * @param location 位置信息
   */
  getCurrentCoordinate(location: BackgroundGeolocationResponse): number[] {
    return [location.longitude, location.latitude];
  }

  /**
   * 启动后台定位服务
   */
  async startBackgroundGeolocation(): Promise<void> {
    try {
      await this.backgroundGeolocation.start();
      console.log('✅ 后台定位服务已启动');
    } catch (error) {
      console.error('❌ 启动后台定位服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止后台定位服务
   */
  stopBackgroundGeolocation(): void {
    this.backgroundGeolocation.stop();
    console.log('🛑 后台定位服务已停止');
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopLocationTracking();
    this.stopBackgroundGeolocation();

    // 清理定位模式变化订阅
    if (this.providerChangeSubscription) {
      this.providerChangeSubscription.unsubscribe();
      this.providerChangeSubscription = null;
    }
  }
} 