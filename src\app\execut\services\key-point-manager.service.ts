import { Injectable } from '@angular/core';
import { Feature } from 'ol';
import Circle from 'ol/geom/Circle';
import Geometry from 'ol/geom/Geometry';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Fill, Stroke, Style } from 'ol/style';
import { KeyPointAlertService } from '../../@core/services/key-point-alert.service';

@Injectable({
  providedIn: 'root'
})
// 关键点管理服务
export class KeyPointManagerService {
  private keyPointLayer: VectorLayer<VectorSource<Geometry>>;
  private keyPointList: any[] = [];

  constructor(private keyPointAlertService: KeyPointAlertService) {
    this.initKeyPointLayer();
  }

  /**
   * 初始化关键点图层
   */
  private initKeyPointLayer(): void {
    this.keyPointLayer = new VectorLayer({ 
      source: new VectorSource(), 
      className: 'key-point' 
    });
  }

  /**
   * 初始化关键点列表
   * @param keyPointList 关键点列表
   */
  initKeyPoints(keyPointList: any[]): void {
    this.keyPointList = keyPointList;
    this.keyPointAlertService.init(keyPointList);
  }

  /**
   * 获取关键点图层
   */
  getKeyPointLayer(): VectorLayer<VectorSource<Geometry>> {
    return this.keyPointLayer;
  }

  /**
   * 获取关键点列表
   */
  getKeyPointList(): any[] {
    return [...this.keyPointList];
  }

  /**
   * 更新位置并计算关键点状态
   * @param coordinate 当前坐标
   */
  updateLocation(coordinate: number[]): void {
    this.keyPointAlertService.updateLocation(coordinate);
    this.calcKeyPointQualified(coordinate);
  }

  /**
   * 计算关键点是否合格
   * @param coordinate 坐标信息
   */
  calcKeyPointQualified(coordinate: number[]): void {
    const features = this.keyPointLayer.getSource().getFeaturesAtCoordinate(coordinate) as Feature<Geometry>[];
    
    features.forEach(feature => {
      if (feature.getGeometry() instanceof Circle) {
        feature.setStyle(new Style({
          fill: new Fill({ color: 'rgba(0, 255, 0, 0.3)' }),
          stroke: new Stroke({
            color: '#0f0',
            width: 2,
          })
        }));
      }
    });
  }

  /**
   * 检查是否在指定范围内
   * @param coordinate 坐标信息
   */
  isInRange(coordinate: number[]): { inRange: boolean, keyPoint?: any } {
    return this.keyPointAlertService.isInRange(coordinate);
  }

  /**
   * 重置关键点样式
   */
  resetKeyPointStyles(): void {
    const features = this.keyPointLayer.getSource().getFeatures();
    features.forEach(feature => {
      feature.setStyle(null); // 使用默认样式
    });
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.keyPointLayer) {
      this.keyPointLayer.getSource().clear();
    }
    this.keyPointList = [];
  }
} 