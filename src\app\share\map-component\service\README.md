# Map Component Services

这个目录包含了地图组件相关的所有服务。

## 服务列表

### 🗺️ MapService (`map.service.ts`)
- **功能**: 地图组件实例管理和标记图层操作
- **主要方法**: 
  - `registerMapComponent()` - 注册地图组件实例
  - `addMarker()` - 添加地图标记
  - `clearMarkers()` - 清除所有标记

### 📍 KeyPointService (`key-point.service.ts`)
- **功能**: 关键点数据管理和自动打卡
- **主要方法**:
  - `getKeyPointsByTaskCode()` - 获取任务关键点
  - `clockInWithCacheBatch()` - 自动打卡（支持离线缓存）

### 🧭 LocationProviderService (`location-provider.service.ts`)
- **功能**: 定位模式管理（GPS、网络、混合定位）
- **主要方法**:
  - `getCurrentProvider()` - 获取当前定位模式
  - `setProvider()` - 设置定位模式
  - `onProviderChange()` - 监听定位模式变化

## 使用方式

### 推荐导入方式
```typescript
// 从统一入口导入
import { MapService, KeyPointService, LocationProviderService } from '../service';
```

### 传统导入方式（不推荐）
```typescript
// 直接导入具体文件（不推荐）
import { MapService } from '../service/map.service';
import { KeyPointService } from '../service/key-point.service';
```

## 定位模式配置

LocationProviderService 支持三种定位模式：

| 模式 | 值 | 描述 | 适用场景 |
|------|----|----- |----------|
| GPS定位 | 1 | 仅使用GPS，精度最高但室内信号差 | 室外巡检 |
| 网络定位 | 2 | 使用网络定位，速度快但精度较低 | 室内巡检 |
| 混合定位 | 3 | GPS+网络混合，平衡精度和速度（默认推荐） | 通用场景 |

## 注意事项

1. **服务注入**: 所有服务都使用 `providedIn: 'root'`，无需在模块中额外声明
2. **内存管理**: 服务中的订阅都应该在组件销毁时正确清理
3. **错误处理**: 所有异步操作都包含适当的错误处理机制
