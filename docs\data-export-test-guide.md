# 数据导出功能测试指南

## 概述

本文档介绍如何测试应用中的数据导出功能，特别是针对同步失败数据的导出功能。

## 功能说明

### 1. 模拟同步失败数据

在个人页面中，我们添加了一个"模拟同步失败数据"功能，用于测试数据导出功能。

**功能位置**: 个人页面 → 模拟同步失败数据

**功能作用**:
- 清空现有的失败数据，确保测试环境干净
- 创建模拟的同步失败数据，包括：
  - 手动打卡失败数据（2条）
  - 自动关键点打卡失败数据（3条）
  - 巡检事件上报失败数据（2条）
- 将这些数据的重试次数设置为最大值（5次），使其成为"失败"状态
- 刷新失败数据数量显示

### 2. 数据导出功能

**功能位置**: 个人页面 → 数据同步管理 → 导出巡检离线数据

**导出内容**:
- 导出时间和设备信息
- 按类型分组的失败数据
- 每条数据的详细信息（ID、数据内容、时间戳、重试次数、上传URL等）

## 测试步骤

### 步骤1: 模拟失败数据

1. 打开应用，进入"个人"页面
2. 点击"模拟同步失败数据"按钮
3. 等待提示消息："已成功模拟 7 条同步失败数据，可以测试导出功能了！"
4. 观察"数据同步管理"项目右侧是否显示红色徽章，显示失败数据数量

### 步骤2: 查看失败数据

1. 点击"数据同步管理"
2. 应该看到弹出的操作选择界面，显示失败数据数量
3. 可以选择以下操作：
   - 手动重试上传
   - 导出巡检离线数据
   - 查看详细信息

### 步骤3: 导出数据

1. 在数据同步管理界面中，点击"导出巡检离线数据"
2. 系统会将失败数据导出为JSON格式的文本文件
3. 文件会保存到设备的文档目录中
4. 查看导出成功的提示消息

### 步骤4: 验证导出内容

导出的JSON文件包含以下结构：

```json
{
  "exportTime": "2025-07-21T01:54:00.000Z",
  "deviceInfo": {
    "userAgent": "...",
    "platform": "..."
  },
  "failedData": [
    {
      "type": "manualClockIn",
      "count": 2,
      "items": [
        {
          "id": "manual_1721523240000_0",
          "data": {
            "taskCode": "TASK_001",
            "userCode": "...",
            "longitude": 108.123456,
            "latitude": 34.567890,
            "clockInTime": 1721519640000,
            "clockInType": "manual",
            "remark": "手动打卡测试数据1"
          },
          "timestamp": "2025-07-21T00:54:00.000Z",
          "retryCount": 5,
          "uploadUrl": "/work-inspect/api/v2/inspect/manual/clock-in"
        }
      ]
    }
  ]
}
```

## 模拟数据详情

### 手动打卡数据（2条）
- 任务代码：TASK_001, TASK_002
- 包含经纬度、打卡时间、打卡类型等信息
- 上传URL：`/work-inspect/api/v2/inspect/manual/clock-in`

### 自动关键点打卡数据（3条）
- 任务代码：TASK_001, TASK_002, TASK_003
- 包含关键点名称、轨迹时间、自动打卡标识等信息
- 上传URL：`/work-inspect/api/v2/inspect/auto/key-point/clock-in`

### 巡检事件上报数据（2条）
- 事件类型：设备异常、安全隐患
- 包含事件级别、描述、图片、附件等信息
- 上传URL：`/work-inspect/api/v2/inspect/batch/event/msg`

## 注意事项

1. **测试环境**: 此功能仅用于测试，不应在生产环境中使用
2. **数据清理**: 每次运行模拟功能时会先清空现有的失败数据
3. **重试次数**: 模拟数据的重试次数设置为5（系统最大重试次数），确保被识别为失败数据
4. **文件位置**: 导出的文件保存在设备的文档目录中，文件名格式为 `inspection_failed_data_YYYYMMDD_HHMMSS.txt`

## 故障排除

### 问题1: 点击模拟按钮后没有反应
- 检查控制台是否有错误信息
- 确认相关服务是否正确注入

### 问题2: 失败数据数量显示为0
- 确认重试次数是否正确设置为最大值
- 检查数据是否正确保存到本地存储

### 问题3: 导出功能无法使用
- 确认设备是否支持文件系统访问
- 检查文件写入权限

## 技术实现

数据模拟功能通过专门的 `TestDataSimulatorService` 服务实现，该服务负责：
- 生成各种类型的模拟失败数据
- 管理数据的生命周期
- 提供清理和重置功能

详细的技术文档请参考：[TestDataSimulatorService 使用文档](./test-data-simulator-service.md)

## 相关文档

- [TestDataSimulatorService 使用文档](./test-data-simulator-service.md) - 开发人员技术文档
- [数据同步完整指南](./data-sync-complete-guide.md) - 数据同步系统文档
