<div class="list-view-container">
  <!-- 筛选工具栏 -->
  <div class="filter-toolbar">
    <!-- 搜索框 -->
    <ion-searchbar 
      placeholder="搜索关键点名称"
      (ionChange)="onSearchChange($event)"
      debounce="300"
      class="search-bar">
    </ion-searchbar>
    
    <!-- 筛选按钮组 -->
    <div class="filter-buttons-container">
      <!-- 巡检类型筛选 -->
      <div class="filter-group">
        <ion-button 
          size="small" 
          [fill]="isInspectionTypeActive('all') ? 'solid' : 'outline'"
          [color]="isInspectionTypeActive('all') ? 'primary' : 'medium'"
          (click)="onInspectionTypeChange('all')">
          <ion-icon name="apps-outline" slot="start"></ion-icon>
          全部
        </ion-button>
        
        <ion-button 
            size="small" 
            [fill]="isInspectionTypeActive('vehicle') ? 'solid' : 'outline'"
            [color]="isInspectionTypeActive('vehicle') ? 'success' : 'medium'"
            (click)="onInspectionTypeChange('vehicle')">
            <ion-icon name="car-outline" slot="start"></ion-icon>
            巡视
          </ion-button>
          
          <ion-button 
            size="small" 
            [fill]="isInspectionTypeActive('person') ? 'solid' : 'outline'"
            [color]="isInspectionTypeActive('person') ? 'warning' : 'medium'"
            (click)="onInspectionTypeChange('person')">
            <ion-icon name="person-outline" slot="start"></ion-icon>
            巡查
          </ion-button>
      </div>
      

    </div>
  </div>

  <!-- 操作工具栏 -->
  <div class="action-toolbar" *ngIf="!isSelectionMode">
    <ion-button fill="outline" size="small" (click)="toggleSelectionMode()">
      <ion-icon name="checkbox-outline" slot="start"></ion-icon>
      批量操作
    </ion-button>
  </div>

  <!-- 选择模式工具栏 -->
  <div class="selection-toolbar" *ngIf="isSelectionMode">
    <div class="selection-info">
      <ion-icon name="checkmark-circle-outline"></ion-icon>
      已选择 {{ selectedItems.size }} 项
    </div>
    <div class="selection-actions">
      <ion-button fill="clear" color="danger" (click)="deleteSelected()" [disabled]="selectedItems.size === 0">
        <ion-icon name="trash-outline" slot="start"></ion-icon>
        删除
      </ion-button>
      <ion-button fill="clear" (click)="toggleSelectionMode()">
        <ion-icon name="close-outline" slot="start"></ion-icon>
        取消
      </ion-button>
    </div>
  </div>

  <!-- 关键点列表 -->
  <ion-content class="keypoint-content">
    <div class="keypoint-list" *ngIf="!loading || keyPoints.length > 0; else loadingTemplate">
      <ion-list>
        <ion-item
          *ngFor="let keyPoint of filteredKeyPoints; let i = index"
          class="keypoint-item"
          [class.inspected]="keyPoint.status === 'inspected'"
          [class.selected]="selectedItems.has(keyPoint.pointCode)">

          <!-- 选择框 -->
          <ion-checkbox
            *ngIf="isSelectionMode"
            slot="start"
            [checked]="selectedItems.has(keyPoint.pointCode)"
            (ionChange)="onItemSelect(keyPoint.pointCode, $event)"
            class="selection-checkbox">
          </ion-checkbox>

          <!-- 序号 -->
          <div class="item-index" slot="start" *ngIf="!isSelectionMode">
            {{ i + 1 }}
          </div>

          <!-- 主要内容 -->
          <ion-label class="keypoint-label" (click)="onKeyPointClick(keyPoint)">
            <div class="keypoint-header">
              <h2 class="keypoint-name">{{ keyPoint.pointName }}</h2>
              <div class="keypoint-badges">
                <!-- 类型标识 -->
                <ion-badge
                  [color]="getTypeColor(keyPoint)"
                  class="type-badge">
                  <ion-icon [name]="keyPoint.type === 'vehicle' ? 'car-outline' : 'person-outline'"></ion-icon>
                  {{ getTypeText(keyPoint) }}
                </ion-badge>
              </div>
            </div>

            <div class="keypoint-details">
              <div class="detail-item">
                <span class="detail-label">巡检类型:</span>
                <span class="detail-value">{{ getTypeText(keyPoint) }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">雨天不巡:</span>
                <span class="detail-value">{{ keyPoint.isItRaining }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">缓冲范围:</span>
                <span class="detail-value">{{ keyPoint.bufferRange }}米</span>
              </div>
            </div>
          </ion-label>

          <!-- 操作按钮 -->
          <div slot="end" class="item-actions" *ngIf="!isSelectionMode">
            <ion-button fill="clear" size="small" (click)="editKeyPoint(keyPoint)" class="edit-button">
              <ion-icon name="create-outline"></ion-icon>
            </ion-button>
            <ion-button fill="clear" size="small" color="danger" (click)="deleteKeyPoint(keyPoint)" class="delete-button">
              <ion-icon name="trash-outline"></ion-icon>
            </ion-button>
          </div>

          <!-- 右侧箭头 (仅在非选择模式下显示) -->
          <ion-icon name="chevron-forward-outline" slot="end" class="chevron-icon" *ngIf="!isSelectionMode"></ion-icon>
        </ion-item>
      </ion-list>
      
      <!-- 空状态 -->
      <div class="empty-state" *ngIf="filteredKeyPoints.length === 0 && !loading">
        <ion-icon name="location-outline" class="empty-icon"></ion-icon>
        <div class="empty-text">暂无关键点数据</div>
        <div class="empty-subtext">请检查筛选条件或稍后重试</div>
      </div>
    </div>
    
    
    <!-- 无限滚动 -->
      <ion-infinite-scroll 
        threshold="100px" 
        class="infinite-scroll"
        *ngIf="keyPoints.length > 0"
        (ionInfinite)="loadMore($event)"
        [disabled]="!hasMoreData || isLoadingMore"
        >
       <ion-infinite-scroll-content
         style="margin-top: 16px;"
         [loadingSpinner]="spinnerType"
         [loadingText]="currentLoadingText">
       </ion-infinite-scroll-content>
     </ion-infinite-scroll>
  </ion-content>

  <!-- 加载状态模板 -->
  <ng-template #loadingTemplate>
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <div class="loading-text">加载中...</div>
    </div>
  </ng-template>
</div>