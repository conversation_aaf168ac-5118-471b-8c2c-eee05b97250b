<div class="list-view-container">
  <!-- 筛选工具栏 -->
  <div class="filter-toolbar">
    <!-- 搜索框 -->
    <ion-searchbar 
      placeholder="搜索关键点名称"
      (ionChange)="onSearchChange($event)"
      debounce="300"
      class="search-bar">
    </ion-searchbar>
    
    <!-- 筛选按钮组 -->
    <div class="filter-buttons-container">
      <!-- 巡检类型筛选 -->
      <div class="filter-group">
        <ion-button 
          size="small" 
          [fill]="isInspectionTypeActive('all') ? 'solid' : 'outline'"
          [color]="isInspectionTypeActive('all') ? 'primary' : 'medium'"
          (click)="onInspectionTypeChange('all')">
          <ion-icon name="apps-outline" slot="start"></ion-icon>
          全部
        </ion-button>
        
        <ion-button 
            size="small" 
            [fill]="isInspectionTypeActive('vehicle') ? 'solid' : 'outline'"
            [color]="isInspectionTypeActive('vehicle') ? 'success' : 'medium'"
            (click)="onInspectionTypeChange('vehicle')">
            <ion-icon name="car-outline" slot="start"></ion-icon>
            巡视
          </ion-button>
          
          <ion-button 
            size="small" 
            [fill]="isInspectionTypeActive('person') ? 'solid' : 'outline'"
            [color]="isInspectionTypeActive('person') ? 'warning' : 'medium'"
            (click)="onInspectionTypeChange('person')">
            <ion-icon name="person-outline" slot="start"></ion-icon>
            巡查
          </ion-button>
      </div>
      

    </div>
  </div>

  <!-- 关键点列表 -->
  <ion-content class="keypoint-content">
    <div class="keypoint-list" *ngIf="!loading || keyPoints.length > 0; else loadingTemplate">
      <ion-list>
        <ion-item 
          *ngFor="let keyPoint of filteredKeyPoints; let i = index"
          (click)="onKeyPointClick(keyPoint)"
          class="keypoint-item"
          [class.inspected]="keyPoint.status === 'inspected'">
          
          <!-- 序号 -->
          <div class="item-index" slot="start">
            {{ i + 1 }}
          </div>
          
          <!-- 主要内容 -->
          <ion-label class="keypoint-label">
            <div class="keypoint-header">
              <h2 class="keypoint-name">{{ keyPoint.pointName }}</h2>
              <div class="keypoint-badges">
                <!-- 类型标识 -->
                <ion-badge 
                  [color]="getTypeColor(keyPoint)"
                  class="type-badge">
                  <ion-icon [name]="keyPoint.type === 'vehicle' ? 'car-outline' : 'person-outline'"></ion-icon>
                  {{ getTypeText(keyPoint) }}
                </ion-badge>
              </div>
            </div>
            
            <div class="keypoint-details">
              <div class="detail-item">
                <span class="detail-label">巡检类型:</span>
                <span class="detail-value">{{ getTypeText(keyPoint) }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">雨天不巡:</span>
                <span class="detail-value">{{ keyPoint.isItRaining }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">缓冲范围:</span>
                <span class="detail-value">{{ keyPoint.bufferRange }}米</span>
              </div>
            </div>
          </ion-label>
          
          <!-- 右侧箭头 -->
          <ion-icon name="chevron-forward-outline" slot="end" class="chevron-icon"></ion-icon>
        </ion-item>
      </ion-list>
      
      <!-- 空状态 -->
      <div class="empty-state" *ngIf="filteredKeyPoints.length === 0 && !loading">
        <ion-icon name="location-outline" class="empty-icon"></ion-icon>
        <div class="empty-text">暂无关键点数据</div>
        <div class="empty-subtext">请检查筛选条件或稍后重试</div>
      </div>
    </div>
    
    
    <!-- 无限滚动 -->
      <ion-infinite-scroll 
        threshold="100px" 
        class="infinite-scroll"
        *ngIf="keyPoints.length > 0"
        (ionInfinite)="loadMore($event)"
        [disabled]="!hasMoreData || isLoadingMore"
        >
       <ion-infinite-scroll-content
         style="margin-top: 16px;"
         [loadingSpinner]="spinnerType"
         [loadingText]="currentLoadingText">
       </ion-infinite-scroll-content>
     </ion-infinite-scroll>
  </ion-content>

  <!-- 加载状态模板 -->
  <ng-template #loadingTemplate>
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <div class="loading-text">加载中...</div>
    </div>
  </ng-template>
</div>