// 列表视图组件样式

.list-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.keypoint-content {
  flex: 1;
  --padding-top: 0;
  --padding-bottom: 0;
}

// 筛选工具栏
.filter-toolbar {
  background: var(--ion-color-light);
  padding: 12px 16px;
  border-bottom: 1px solid var(--ion-color-light-shade);
  
  .search-bar {
    margin-bottom: 12px;
    --background: white;
    --border-radius: 12px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .filter-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .filter-group {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      
      ion-button {
        --border-radius: 16px;
        --padding-start: 10px;
        --padding-end: 10px;
        --padding-top: 4px;
        --padding-bottom: 4px;
        height: 28px;
        font-size: 11px;
        font-weight: 500;
        
        ion-icon {
          font-size: 12px;
          margin-right: 3px;
        }
      }
    }
  }
}

// 操作工具栏
.action-toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid var(--ion-color-light-shade);
  background: var(--ion-color-light);

  ion-button {
    --border-radius: 8px;
    --padding-start: 12px;
    --padding-end: 12px;
    height: 32px;
    font-size: 12px;
    font-weight: 500;

    ion-icon {
      font-size: 14px;
      margin-right: 4px;
    }
  }
}

// 选择模式工具栏
.selection-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--ion-color-primary);
  color: white;
  border-bottom: 1px solid var(--ion-color-primary-shade);

  .selection-info {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;

    ion-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .selection-actions {
    display: flex;
    gap: 4px;

    ion-button {
      --color: white;
      --border-color: rgba(255, 255, 255, 0.3);
      --border-radius: 6px;
      height: 28px;
      font-size: 12px;

      ion-icon {
        font-size: 14px;
        margin-right: 3px;
      }

      &[color="danger"] {
        --color: var(--ion-color-danger);
        --background: rgba(255, 255, 255, 0.1);
        --border-color: rgba(var(--ion-color-danger-rgb), 0.5);

        &:hover {
          --background: rgba(var(--ion-color-danger-rgb), 0.1);
        }
      }
    }
  }
}

// 关键点列表
.keypoint-list {
  padding-bottom: 50px;
  
  ion-list {
    padding: 0 0 20px 0;
  }
}

.keypoint-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --border-color: var(--ion-color-light-shade);
  margin-bottom: 8px;
  border-radius: 12px;
  margin: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  
  &.inspected {
    --background: rgba(76, 175, 80, 0.05);
    border-left: 4px solid var(--ion-color-success);
  }

  &.selected {
    --background: rgba(var(--ion-color-primary-rgb), 0.1);
    border: 2px solid var(--ion-color-primary);
    transform: scale(0.98);
    transition: all 0.2s ease;
  }

  .selection-checkbox {
    margin-right: 12px;
    --size: 20px;
    --checkmark-color: var(--ion-color-primary);
    --border-color: var(--ion-color-medium);
    --border-color-checked: var(--ion-color-primary);
    --background-checked: var(--ion-color-primary);
  }

  .item-actions {
    display: flex;
    gap: 4px;
    margin-left: 8px;

    .edit-button,
    .delete-button {
      --padding-start: 6px;
      --padding-end: 6px;
      --padding-top: 6px;
      --padding-bottom: 6px;
      width: 32px;
      height: 32px;
      --border-radius: 8px;

      ion-icon {
        font-size: 16px;
      }
    }

    .edit-button {
      --color: var(--ion-color-primary);
      --background-hover: rgba(var(--ion-color-primary-rgb), 0.1);

      &:hover {
        --background: rgba(var(--ion-color-primary-rgb), 0.1);
      }
    }

    .delete-button {
      --color: var(--ion-color-danger);
      --background-hover: rgba(var(--ion-color-danger-rgb), 0.1);

      &:hover {
        --background: rgba(var(--ion-color-danger-rgb), 0.1);
      }
    }
  }

  .chevron-icon {
    color: var(--ion-color-medium);
    font-size: 16px;
    margin-left: 8px;
  }

  .item-index {
    background: var(--ion-color-primary);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-right: 12px;
  }
  
  .keypoint-label {
    .keypoint-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
      
      .keypoint-name {
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0;
        flex: 1;
      }
      
      .keypoint-badges {
        display: flex;
        gap: 4px;
        flex-shrink: 0;
        
        .weather-badge,
        .type-badge {
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 8px;
          
          ion-icon {
            font-size: 10px;
            margin-right: 2px;
          }
        }
      }
    }
    
    .keypoint-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .detail-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        
        .detail-label {
          color: var(--ion-color-medium);
          font-weight: 500;
          min-width: 60px;
        }
        
        .detail-value {
          color: var(--ion-color-dark);
          font-weight: 400;
        }
        
        .status-badge {
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 8px;
          
          ion-icon {
            font-size: 10px;
            margin-right: 2px;
          }
        }
      }
    }
  }
  
  .chevron-icon {
    color: var(--ion-color-medium);
    font-size: 16px;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-light-shade);
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-medium);
    margin-bottom: 8px;
  }
  
  .empty-subtext {
    font-size: 16px;
    color: var(--ion-color-light-shade);
  }
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  .loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: var(--ion-color-medium);
  }
}

// 无限滚动样式
.infinite-scroll {
  ion-infinite-scroll-content {
    text-align: center;
    
    .infinite-loading {
      margin: 16px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filter-toolbar {
    padding: 8px 12px;
    
    .search-bar {
      margin-bottom: 8px;
    }
    
    .filter-buttons-container {
      gap: 6px;
      
      .filter-group {
        gap: 4px;
        
        ion-button {
          --padding-start: 8px;
          --padding-end: 8px;
          height: 26px;
          font-size: 10px;
          
          ion-icon {
            font-size: 11px;
            margin-right: 2px;
          }
        }
      }
    }
  }

  .action-toolbar {
    padding: 6px 12px;

    ion-button {
      height: 28px;
      font-size: 11px;
      --padding-start: 10px;
      --padding-end: 10px;

      ion-icon {
        font-size: 12px;
      }
    }
  }

  .selection-toolbar {
    padding: 6px 12px;

    .selection-info {
      font-size: 12px;

      ion-icon {
        font-size: 14px;
      }
    }

    .selection-actions {
      gap: 3px;

      ion-button {
        height: 26px;
        font-size: 11px;

        ion-icon {
          font-size: 12px;
        }
      }
    }
  }

  .keypoint-item {
    margin: 6px 12px;
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 10px;
    --padding-bottom: 10px;
    
    .item-index {
      width: 20px;
      height: 20px;
      font-size: 11px;
      margin-right: 10px;
    }

    .selection-checkbox {
      --size: 18px;
      margin-right: 10px;
    }

    .item-actions {
      gap: 2px;
      margin-left: 4px;

      .edit-button,
      .delete-button {
        width: 28px;
        height: 28px;
        --padding-start: 4px;
        --padding-end: 4px;
        --padding-top: 4px;
        --padding-bottom: 4px;

        ion-icon {
          font-size: 14px;
        }
      }
    }

    .chevron-icon {
      font-size: 14px;
      margin-left: 4px;
    }

    .keypoint-label {
      .keypoint-header {
        .keypoint-name {
          font-size: 16px;
        }
      }
      
      .keypoint-details {
        .detail-item {
          font-size: 13px;
          
          .detail-label {
            min-width: 50px;
          }
        }
      }
    }
  }
}

