import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { EventReportInfo } from '../class/home';
import { HomeModuleService } from '../home.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PageEventService } from '../home.event';
import { DatePipe } from '@angular/common';
import { ModalController } from '@ionic/angular';
import { IncidentListComponent } from 'src/app/statistics/incident-list/incident-list.component';

@Component({
  selector: 'app-today-report',
  templateUrl: './today-report.component.html',
  styleUrls: ['./today-report.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TodayReportComponent implements OnInit, OnDestroy {
  // 当前时间
  currentTime = this.dateTransform(new Date(), 'yyyy-MM-dd');
  // 事项上报统计
  problemList = EventReportInfo;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private netSer: HomeModuleService, private dataEvent: PageEventService,
    private cd: ChangeDetectorRef, private datePipe: DatePipe,
    private modalController: ModalController,
  ) { }

  ngOnInit(): void {
    // 页面数据更新
    this.dataEvent.receiveDataForPageType('home')
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: boolean) => {
        if (state) {
          this.loadData();
        }
      });
    this.loadData();
  }

  loadData() {
    this.netSer.todayReported()
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res.code === 0) {
          this.problemList = res.data;
        }
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

  async onProblemClick(selectId: string): Promise<void> {
    const modal = await this.modalController.create({
      component: IncidentListComponent,
      componentProps: {
        createTime: this.currentTime,
        activeTabId: selectId
      },
      backdropDismiss: false
    });
    await modal.present();
  }

  /**
   * 时间转换器
   */
  dateTransform(date: any, format: string): any {
    return this.datePipe.transform(date ? new Date(date) : new Date(), format);
  }

}
