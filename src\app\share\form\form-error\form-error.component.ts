import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { OstFormItemService } from '../ost-form.service';

@Component({
  selector: 'ost-error',
  templateUrl: './form-error.component.html',
  styleUrls: ['./form-error.component.scss']
})
export class FormErrorComponent implements OnInit {

  @Input() formGroup: FormGroup;
  @Input() ostFormControlNames: string;
  @Input() errorCode: string;
  constructor(private ostFormItemServe: OstFormItemService) { }

  ngOnInit(): void {
    setTimeout(() => {
      this.formGroup = this.formGroup || this.ostFormItemServe.formGroup;
      this.ostFormControlNames = this.ostFormControlNames || this.ostFormItemServe.ostFormControlNames;
    }, 200);
  }

}
