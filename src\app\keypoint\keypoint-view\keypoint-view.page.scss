// 关键点查看页面样式

// 页面整体布局
:host {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 内容区域
ion-content {
  --padding-top: 0;
  --padding-bottom: 0;
  --overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Tab导航
.tab-segment {
  margin: 8px 16px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  ion-segment-button {
    --indicator-color: var(--ion-color-primary);
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary);
    
    ion-icon {
      font-size: 16px;
    }
    
    ion-label {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 视图容器
.view-container {
  flex: 1;
  position: relative;
  min-height: 0;
  height: 100%;
}

// 地图筛选面板
.map-filter-panel {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



// 地图容器
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  
  ost-map {
    width: 100%;
    height: 100%;
    display: block;
  }
  
  // 固定图例面板
    .legend-panel-fixed {
      position: absolute;
      bottom: 85px;
      left: 8px;
      z-index: 1001;
      background: rgba(255, 255, 255, 0.85);
      backdrop-filter: blur(8px);
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      min-width: 100px;
      max-width: 140px;
      
      .legend-header {
        padding: 4px 8px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        
        .legend-title {
          font-size: 10px;
          font-weight: 600;
          color: var(--ion-color-dark);
        }
      }
      
      .legend-content {
        padding: 4px 8px;
        
        .legend-item {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 1px 0;
          
          .legend-icon {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 4px solid;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            
            .inner-circle {
              width: 6px;
              height: 6px;
              background: white;
              border-radius: 50%;
            }
          }
          
          .legend-label {
            font-size: 11px;
            color: var(--ion-color-dark);
            white-space: nowrap;
          }
        }
      }
    }
}

// 加载状态
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 2000;
  
  .loading-text {
    color: var(--ion-color-medium);
    font-size: 16px;
  }
}

// 紧凑头部
.compact-header {
  ion-toolbar {
    --min-height: 44px;
    
    ion-title {
      font-size: 18px;
      font-weight: 500;
    }
  }
}

// 暂无数据样式
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: var(--ion-color-medium);
  
  ion-icon {
    margin-bottom: 16px;
    opacity: 0.6;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 无限滚动样式
.infinite-scroll {
  ion-infinite-scroll-content {
    text-align: center;
    
    .infinite-loading {
      margin: 16px 0;
    }
  }
}