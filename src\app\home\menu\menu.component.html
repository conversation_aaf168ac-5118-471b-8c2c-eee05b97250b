<ion-card-content style="padding: 0; background: white; margin-bottom: 12px; margin-top: 42px">
  <ng-container>
    <ion-grid>
      <ion-row class="ion-align-items-center">
        <!-- 实时监控 -->
        <ion-col size="3">
          <div class="div-box" (click)="onSubItemClick('app/monitor')">
            <div class="p-img" style="background: #46b0de;">
              <img class="img" src="assets/menu/monitor.png" />
            </div>
            <div class="name">实时监控</div>
          </div>
        </ion-col>
        <!-- 巡检统计 -->
        <ion-col size="3">
          <div class="div-box" (click)="onSubItemClick('app/statistics')">
            <div class="p-img" style="background: #5499f7;">
              <img class="img" src="assets/menu/statistics.png" />
            </div>
            <div class="name">巡检统计</div>
          </div>
        </ion-col>
        <!-- 报警信息 -->
        <ion-col size="3">
          <div class="div-box" (click)="onSubItemClick('app/alarm')">
            <div class="p-img" style="background: #c7000b;">
              <img class="img" src="assets/menu/alarm.png" />
            </div>
            <div class="name">报警信息</div>
          </div>
        </ion-col>
        <!-- 关键点管理 -->
        <ion-col size="3" *ngIf="hasKeyPointPermission()">
          <div class="div-box" (click)="onSubItemClick('app/keypoint')">
            <div class="p-img" style="background: #ff9500;">
              <img class="img" src="assets/menu/search_location.png" />
            </div>
            <div class="name">关键点查看</div>
          </div>
        </ion-col>
        <!-- 关键点审批 -->
        <ion-col size="3" *ngIf="hasKeyPointApprovalPermission()">
          <div class="div-box" (click)="onSubItemClick('app/keypoint/confirm')">
            <div class="p-img" style="background: #28a745; position: relative;">
              <img class="img" src="assets/menu/approval.png" />
              <!-- 未确认数量徽章 -->
              <div class="badge" *ngIf="unconfirmedCount > 0">
                {{unconfirmedCount > 99 ? '99+' : unconfirmedCount}}
              </div>
            </div>
            <div class="name">关键点确认</div>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ng-container>
</ion-card-content>